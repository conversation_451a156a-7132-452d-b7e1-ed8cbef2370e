package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.JDA;
import net.dv8tion.jda.api.JDABuilder;
import net.dv8tion.jda.api.entities.Activity;

import net.dv8tion.jda.api.requests.GatewayIntent;
import shyrcs.extrastoragehook.SbMagicHook;
import shyrcs.extrastoragehook.application.Library;

/**
 * Discord Bot implementation using JDA only (Discord4J disabled)
 */
public class BotImpl {

    private JDA jda;
    private final String token;

    public BotImpl(String token) {
        this.token = token;
        run();
    }

    /**
     * Khởi động bot (chỉ JDA)
     */
    public void run() {
        new Thread(() -> {
            try {
                startJDA();
            } catch (Exception e) {
                SbMagicHook.error("Lỗi khi khởi động JDA Discord bot: " + e.getMessage());
                e.printStackTrace();
            }
        }).start();
    }

    /**
     * Khởi động JDA bot
     */
    private void startJDA() throws Exception {


        JDABuilder builder = JDABuilder.createDefault(token);
        builder.enableIntents(GatewayIntent.MESSAGE_CONTENT);

        // Thiết lập activity
        Activity activity = buildActivity(
            Library.config.getActivityType(),
            Library.config.getActivity()
        );
        if (activity != null) {
            builder.setActivity(activity);
        }

        // Thiết lập status (default: ONLINE)
        builder.setStatus(net.dv8tion.jda.api.OnlineStatus.ONLINE);

        // Đăng ký listeners
        builder.addEventListeners(new SlashListener());
        builder.addEventListeners(new ButtonListener());
        builder.addEventListeners(new MessageListener());

        this.jda = builder.build();
        this.jda.awaitReady();



        // Đăng ký slash commands
        BotSetup.registerSlashCommandsSimple(jda);
    }

    /**
     * Tắt bot
     */
    public void shutdown() {
        if (jda != null) {
            SbMagicHook.info("🛑 Đang dừng JDA bot...");
            jda.shutdown();
            SbMagicHook.info("✅ JDA bot đã dừng!");
        }
    }

    /**
     * Lấy JDA instance
     */
    public JDA getJDA() {
        return jda;
    }

    /**
     * Gửi private message (chỉ JDA)
     */
    public void sendPrivateMessage(String userId, String message) {
        try {
            if (jda != null) {
                // Sử dụng JDA
                jda.retrieveUserById(userId).queue(user -> {
                    user.openPrivateChannel().queue(channel -> {
                        channel.sendMessage(message).queue(
                            success -> SbMagicHook.info("Đã gửi private message cho user: " + userId),
                            error -> SbMagicHook.error("Lỗi khi gửi private message: " + error.getMessage())
                        );
                    });
                });
            } else {
                SbMagicHook.warn("JDA bot chưa sẵn sàng để gửi private message");
            }
        } catch (Exception e) {
            SbMagicHook.error("Lỗi khi gửi private message: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Build activity từ config
     */
    private Activity buildActivity(String type, String text) {
        if (text == null || text.isEmpty()) return null;

        switch (type.toLowerCase()) {
            case "playing":
                return Activity.playing(text);
            case "listening":
                return Activity.listening(text);
            case "watching":
                return Activity.watching(text);
            case "competing":
                return Activity.competing(text);
            default:
                return Activity.playing(text);
        }
    }

}
