package shyrcs.dragon.dragon.skills;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.CustomDragon;

/**
 * Rush skill - Dragon dives down and damages players in impact area
 */
public class RushSkill extends DragonSkill {
    
    public RushSkill(SoulDragonPlugin plugin, CustomDragon customDragon) {
        super(plugin, customDragon, "rush");
    }
    
    @Override
    public boolean canUse() {
        return customDragon.getCurrentTarget() != null && 
               customDragon.getDragon().getLocation().getY() > customDragon.getCurrentTarget().getLocation().getY() + 10;
    }
    
    @Override
    public void use() {
        Player target = customDragon.getCurrentTarget();
        if (target == null) {
            return;
        }
        
        Location dragonLocation = customDragon.getDragon().getLocation();
        Location targetLocation = target.getLocation();
        
        // Calculate rush trajectory
        Vector direction = targetLocation.toVector().subtract(dragonLocation.toVector()).normalize();
        
        // Start rush sequence
        new BukkitRunnable() {
            private int ticks = 0;
            private final Location startLocation = dragonLocation.clone();
            private final Location endLocation = targetLocation.clone();
            
            @Override
            public void run() {
                ticks++;
                
                if (ticks > 40) { // 2 seconds
                    this.cancel();
                    return;
                }
                
                // Calculate current position
                double progress = ticks / 40.0;
                Location currentLocation = startLocation.clone().add(
                    endLocation.clone().subtract(startLocation).multiply(progress)
                );
                
                // Move dragon
                customDragon.getDragon().teleport(currentLocation);
                
                // Add particle effects
                currentLocation.getWorld().spawnParticle(
                    Particle.FLAME, 
                    currentLocation, 
                    10, 
                    0.5, 0.5, 0.5, 
                    0.1
                );
                
                // Check for impact
                if (ticks >= 35) { // Near end of rush
                    performImpact(currentLocation);
                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);
        
        // Play sound
        dragonLocation.getWorld().playSound(dragonLocation, Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 0.8f);
    }
    
    /**
     * Perform impact damage
     */
    private void performImpact(Location impactLocation) {
        double damage = getDamage();
        double range = getRange();
        
        // Create explosion effect
        impactLocation.getWorld().spawnParticle(
            Particle.EXPLOSION, 
            impactLocation, 
            5, 
            2.0, 2.0, 2.0, 
            0.0
        );
        
        impactLocation.getWorld().playSound(impactLocation, Sound.ENTITY_GENERIC_EXPLODE, 2.0f, 1.0f);
        
        // Damage nearby entities
        for (Entity entity : impactLocation.getWorld().getNearbyEntities(impactLocation, range, range, range)) {
            if (entity instanceof LivingEntity livingEntity && 
                !(entity.equals(customDragon.getDragon()))) {
                
                double distance = entity.getLocation().distance(impactLocation);
                if (distance <= range) {
                    // Calculate damage based on distance
                    double actualDamage = damage * (1.0 - (distance / range));
                    
                    livingEntity.damage(actualDamage);
                    
                    // Knockback effect
                    Vector knockback = entity.getLocation().toVector()
                        .subtract(impactLocation.toVector())
                        .normalize()
                        .multiply(2.0);
                    
                    entity.setVelocity(knockback);
                }
            }
        }
    }
    
    @Override
    public int getCooldownSeconds() {
        return plugin.getConfig().getInt("dragon.skills.rush.cooldown", 15);
    }
}
