package shyrcs.Ability;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

/**
 * Listener xử lý khi player sử dụng item có BuffConfig stat
 */
public class BuffConfigListener implements Listener {
    
    private final CooldownManager cooldownManager;
    
    public BuffConfigListener(org.bukkit.plugin.Plugin plugin) {
        this.cooldownManager = CooldownManager.getInstance(plugin);
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (item == null || !item.hasItemMeta()) {
            return;
        }
        
        // Kiểm tra xem có phải là MMOItem không và có BuffConfig stat không
        NBTItem nbtItem = NBTItem.get(item);
        if (!nbtItem.hasTag("MMOITEMS_BUFF_CONFIG")) {
            return;
        }

        String buffConfigString = nbtItem.getString("MMOITEMS_BUFF_CONFIG");
        if (buffConfigString == null || buffConfigString.trim().isEmpty()) {
            return;
        }
        
        // Parse buff config
        BuffConfigStat.BuffConfig buffConfig = BuffConfigStat.parseBuffConfig(buffConfigString);
        if (buffConfig == null) {
            player.sendMessage("§cCấu hình buff không hợp lệ trên item này!");
            return;
        }
        
        Action action = event.getAction();
        
        // Xác định loại click
        String clickType = getClickType(action, player.isSneaking());
        if (clickType == null) {
            return; // Không phải click hợp lệ
        }
        
        // Kiểm tra xem click type có khớp không
        if (!clickType.equals(buffConfig.getClickType())) {
            return; // Click type không khớp
        }
        
        // Kiểm tra cooldown
        String cooldownKey = "buff_" + buffConfig.getBuffId().toLowerCase();
        if (cooldownManager.isOnCooldown(player, cooldownKey)) {
            long remainingTime = cooldownManager.getRemainingCooldown(player, cooldownKey);
            String cooldownMessage = "§c⏱ " + buffConfig.getBuffId() + " Cooldown: " + BuffUtils.formatCooldownTime(remainingTime);
            BuffUtils.sendActionBar(player, cooldownMessage);
            event.setCancelled(true);
            return;
        }
        
        // Áp dụng buff
        applyBuffFromConfig(player, buffConfig);
        
        // Đặt cooldown
        if (buffConfig.getCooldown() > 0) {
            cooldownManager.setCooldown(player, cooldownKey, buffConfig.getCooldown());
        }
        
        event.setCancelled(true);
    }
    
    /**
     * Xác định loại click
     */
    private String getClickType(Action action, boolean isShiftClick) {
        if (action == Action.LEFT_CLICK_AIR || action == Action.LEFT_CLICK_BLOCK) {
            return isShiftClick ? "shift_left_click" : "left_click";
        } else if (action == Action.RIGHT_CLICK_AIR || action == Action.RIGHT_CLICK_BLOCK) {
            return isShiftClick ? "shift_right_click" : "right_click";
        }
        return null;
    }
    
    /**
     * Áp dụng buff từ config
     */
    private void applyBuffFromConfig(Player player, BuffConfigStat.BuffConfig buffConfig) {
        PotionEffectType effectType = buffConfig.getPotionEffectType();
        if (effectType == null) {
            player.sendMessage("§cBuff type không hợp lệ: " + buffConfig.getBuffId());
            return;
        }
        
        // Tạo potion effect
        PotionEffect effect = new PotionEffect(
            effectType, 
            buffConfig.getDurationTicks(), 
            buffConfig.getAmplifier()
        );
        
        // Áp dụng buff
        player.addPotionEffect(effect);

        // Track skill buff để hiển thị khi sắp hết
        cooldownManager.trackSkillBuff(player, effectType.getKey().getKey().toUpperCase(), buffConfig.getDuration());
        
        // Gửi thông báo
        String buffNameVi = BuffPlaceholderUtil.getBuffNameVi(effectType);
        String levelRoman = BuffUtils.toRoman(buffConfig.getLevel());
        
        player.sendMessage("§a✓ Đã kích hoạt buff " + buffNameVi + " " + levelRoman);
        player.sendMessage("§e⏱ Thời gian: " + buffConfig.getDuration() + " giây");
        
        if (buffConfig.getCooldown() > 0) {
            player.sendMessage("§7⏳ Cooldown: " + buffConfig.getCooldown() + " giây");
        }
        
        // Tạo và gọi BuffEvent
        BuffEvent buffEvent = new BuffEvent(
            player, 
            effectType, 
            buffConfig.getDuration(), 
            buffConfig.getAmplifier(), 
            buffConfig.getClickType(), 
            buffConfig.getCooldown(), 
            buffConfig.getLevel()
        );
        Bukkit.getPluginManager().callEvent(buffEvent);
    }
    
    /**
     * Kiểm tra xem item có BuffConfig stat không
     */
    public static boolean hasBuffConfig(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }

        NBTItem nbtItem = NBTItem.get(item);
        return nbtItem.hasTag("MMOITEMS_BUFF_CONFIG");
    }

    /**
     * Lấy BuffConfig từ item
     */
    public static BuffConfigStat.BuffConfig getBuffConfig(ItemStack item) {
        if (!hasBuffConfig(item)) {
            return null;
        }

        NBTItem nbtItem = NBTItem.get(item);
        String buffConfigString = nbtItem.getString("MMOITEMS_BUFF_CONFIG");

        return BuffConfigStat.parseBuffConfig(buffConfigString);
    }
}
