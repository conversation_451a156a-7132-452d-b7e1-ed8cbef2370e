package shyrcs.Ability;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerPickupItemEvent;
import org.bukkit.event.entity.EntityPickupItemEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.Bukkit;

/**
 * Listener để xử lý signature logic
 */
public class SignatureListener implements Listener {
    
    /**
     * Xử lý khi player pickup item
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onEntityPickupItem(EntityPickupItemEvent event) {
        if (!(event.getEntity() instanceof Player)) return;
        
        Player player = (Player) event.getEntity();
        ItemStack item = event.getItem().getItemStack();
        
        processItemSignature(player, item);
    }
    
    /**
     * X<PERSON> lý khi player cầm item mới
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onPlayerItemHeld(PlayerItemHeldEvent event) {
        try {
            ItemStack item = event.getPlayer().getInventory().getItem(event.getNewSlot());
            if (item != null) {
                processItemSignature(event.getPlayer(), item);
                
                // Xử lý placeholder với delay nhỏ để tránh conflict
                Bukkit.getScheduler().runTaskLater(
                    Bukkit.getPluginManager().getPlugin("SoulSkills"), 
                    () -> {
                        ItemStack processedItem = processPlaceholderForItem(item);
                        if (processedItem != null) {
                            event.getPlayer().getInventory().setItem(event.getNewSlot(), processedItem);
                        }
                    }, 
                    1L
                );
            }
        } catch (Exception e) {
            // Ignore errors để tránh spam console
        }
    }
    
    /**
     * Xử lý khi player click vào item trong inventory
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        try {
            Player player = (Player) event.getWhoClicked();
            ItemStack item = event.getCurrentItem();
            if (item != null) {
                processItemSignature(player, item);
                
                // Xử lý placeholder với delay nhỏ
                Bukkit.getScheduler().runTaskLater(
                    Bukkit.getPluginManager().getPlugin("SoulSkills"), 
                    () -> {
                        ItemStack processedItem = processPlaceholderForItem(item);
                        if (processedItem != null) {
                            event.setCurrentItem(processedItem);
                        }
                    }, 
                    1L
                );
            }
        } catch (Exception e) {
            // Ignore errors
        }
    }
    
    /**
     * Kiểm tra signature trước khi sử dụng skill (PlayerInteractEvent)
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        if (item != null && SignaturePlaceholderUtil.hasSignatureStat(item)) {
            // Kiểm tra xem player có phải chủ nhân không
            if (!isPlayerAllowedToUse(player, item)) {
                event.setCancelled(true);
                player.sendMessage("§c[Signature] §7Bạn không phải chủ nhân của item này!");
                return;
            }
        }
    }
    
    /**
     * Kiểm tra signature trước khi đào block (BlockBreakEvent)
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        
        if (item != null && SignaturePlaceholderUtil.hasSignatureStat(item)) {
            // Kiểm tra xem player có phải chủ nhân không
            if (!isPlayerAllowedToUse(player, item)) {
                event.setCancelled(true);
                player.sendMessage("§c[Signature] §7Bạn không phải chủ nhân của item này!");
                return;
            }
        }
    }
    
    /**
     * Xử lý signature cho item (simplified approach)
     */
    private void processItemSignature(Player player, ItemStack item) {
        if (item == null || !SignaturePlaceholderUtil.hasSignatureStat(item)) return;

        // For signature items, we'll check ownership during usage instead of setting it here
        // The owner should be set via MMOItems commands when creating the item
        // This method can be used for future enhancements
    }
    
    /**
     * Xử lý placeholder cho item (đã đơn giản hóa)
     */
    private ItemStack processPlaceholderForItem(ItemStack item) {
        // Signature system đã được đơn giản hóa
        // Không còn cần xử lý placeholder
        return null;
    }
    
    /**
     * Kiểm tra xem player có được phép sử dụng item không (đã đơn giản hóa)
     */
    private boolean isPlayerAllowedToUse(Player player, ItemStack item) {
        // Staff có OP bỏ qua kiểm tra
        if (player.isOp()) {
            return true;
        }

        // Signature system đã được đơn giản hóa
        // Chỉ chặn tất cả non-OP players
        return false;
    }
}
