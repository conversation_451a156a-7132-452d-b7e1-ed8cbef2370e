# SoulDragonCustom - Dragon Battle System

A comprehensive custom dragon battle system for Minecraft servers with setup items, summoning rituals, and advanced boss mechanics.

## Features

### Setup System
- **Boss Position Setup**: Use 1 Fire Rod to mark boss spawn location (130 blocks above)
- **Altar Setup**: Use 8 Fire Rods on End Portal Frame to create summoning altar
- **Crystal Setup**: Use 12 Fire Rods to mark End Crystal spawn positions

### Summoning Ritual
- Place 8 custom Ender Eyes on altars to start summoning
- 12-second ritual with End Crystal spawning and epic poem
- Dramatic dragon spawn with explosion effects

### Custom Dragon Boss: "Rồng Ngàn Năm"
- **Health**: 2000 HP with white boss bar
- **AI**: Stays in designated area, intelligent targeting
- **Skills**:
  - **Rush**: Dive attack with area damage
  - **Dragon's Breath**: Fire projectiles with knockup
  - **Lightning Strike**: Unavoidable lightning attacks
  - **Crystal Heal**: Spawns healing crystals
  - **Roar**: Area debuffs and knockback
  - **Shadow Clones**: Deceptive illusions

## Installation

1. Place the plugin JAR in your server's `plugins` folder
2. Restart the server
3. Configure the plugin in `plugins/SoulDragonCustom/config.yml`

## Dependencies

- **Required**: Paper/Spigot 1.21+
- **Optional**: HeadDatabase (for custom textures)
- **Optional**: MythicMobs (for enhanced effects)

## Commands

### Admin Commands
- `/sdc give <type> [player]` - Give setup items
  - Types: `boss`, `altar`, `crystal`, `eye`
- `/sdc remove all` - Remove all setup data
- `/sdc reload` - Reload configuration
- `/sdc info` - Show setup statistics

### Setup Items
- **Boss Position**: `/sdc give boss` - Marks dragon spawn location
- **Altar**: `/sdc give altar` - Creates summoning altar (End Portal Frame only)
- **Crystal**: `/sdc give crystal` - Marks End Crystal positions
- **Ender Eye**: `/sdc give eye` - For summoning ritual

## Permissions

- `souldragon.admin` - Access to all commands (default: op)
- `souldragon.setup` - Use setup items (default: op)
- `souldragon.summon` - Summon dragon (default: true)

## Setup Guide

### 1. Mark Boss Spawn Location
1. Get boss position item: `/sdc give boss`
2. Right-click on any block where you want the dragon to spawn
3. Dragon will spawn 130 blocks above this location

### 2. Create Summoning Altar
1. Build End Portal Frame structure
2. Get altar item: `/sdc give altar`
3. Right-click on End Portal Frame blocks to register them as altars

### 3. Mark Crystal Positions
1. Get crystal item: `/sdc give crystal`
2. Right-click on blocks where End Crystals should spawn during summoning
3. Recommend 12+ positions around the battle area

### 4. Test Summoning
1. Get Ender Eyes: `/sdc give eye`
2. Right-click on registered altar blocks with Ender Eyes
3. Place all 8 eyes to start the summoning ritual

## Configuration

Key configuration options in `config.yml`:

```yaml
dragon:
  name: "§f§lRồng Ngàn Năm"
  health: 2000.0
  
summoning:
  poem: # Custom poem lines
  crystal_spawn_interval: 1
  
setup_items:
  boss_position:
    cost: 1
  altar:
    cost: 8
  crystal:
    cost: 12
```

## Troubleshooting

### Common Issues

1. **Dragon not spawning**
   - Ensure boss position is set
   - Check that altar is properly registered
   - Verify all 8 Ender Eyes are placed

2. **Setup items not working**
   - Check permissions (`souldragon.setup`)
   - Ensure sufficient items in inventory
   - For altars, only End Portal Frames work

3. **Database errors**
   - Check file permissions in plugin folder
   - Restart server if database is corrupted

### Debug Commands
- `/sdc info` - Check setup statistics
- Check console for detailed error messages

## Technical Details

### Database
- SQLite database stores all setup locations
- Automatic cleanup on plugin disable
- Persistent across server restarts

### Performance
- Optimized AI with configurable tick rates
- Efficient particle systems
- Automatic cleanup of temporary entities

### Compatibility
- Paper/Spigot 1.21+
- Java 17+
- Compatible with most plugins

## Support

For issues or questions:
1. Check console logs for errors
2. Verify configuration syntax
3. Test with minimal plugin setup
4. Report bugs with full error logs

## Version History

### v1.0.0
- Initial release
- Complete dragon battle system
- All skills implemented
- Setup system functional
