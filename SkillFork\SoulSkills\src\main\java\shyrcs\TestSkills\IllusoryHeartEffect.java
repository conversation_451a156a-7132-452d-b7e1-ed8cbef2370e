package shyrcs.TestSkills;

import org.bukkit.*;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Illusory Heart (Ảo Ảnh Tâm Can) - Shrine of Maya Effect
 * Tạo một mái vòm Maya với các buff khác nhau dựa trên số lượng player
 */
public class IllusoryHeartEffect {
    
    private static final double DOME_RADIUS = 25.0; // B<PERSON> kính 25 blocks
    private static final int DOME_DURATION = 300; // 15 giây (300 ticks)
    
    private final Plugin plugin;
    private static final Map<UUID, IllusoryHeartInstance> activeDomes = new ConcurrentHashMap<>();
    
    public IllusoryHeartEffect(Plugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Tạo Shrine of Maya tại vị trí player
     */
    public void createShrineOfMaya(Player caster, double damageBoost, double lightningDamage, 
                                   double healPerSecond, double speedBoost, int cooldownSeconds) {
        
        // Kiểm tra cooldown
        if (activeDomes.containsKey(caster.getUniqueId())) {
            caster.sendMessage("§c[Illusory Heart] §7Shrine of Maya đang hoạt động!");
            return;
        }
        
        Location center = caster.getLocation().clone();
        // Không nâng center lên, để ở cùng level với player để mobs dễ vào dome hơn
        // center.setY(center.getY() + 1); // Nâng lên 1 block


        
        // Tạo instance mới
        IllusoryHeartInstance instance = new IllusoryHeartInstance(
            caster, center, damageBoost, lightningDamage, healPerSecond, speedBoost, DOME_DURATION
        );
        
        activeDomes.put(caster.getUniqueId(), instance);
        
        // Bắt đầu hiệu ứng
        startShrineEffect(instance);
        
        // Thông báo (chỉ một lần)
        caster.sendMessage("§a[Illusory Heart] §7Shrine of Maya đã được triển khai!");
        
        // Hiệu ứng âm thanh triển khai
        center.getWorld().playSound(center, Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.2f);
        center.getWorld().playSound(center, Sound.BLOCK_ENCHANTMENT_TABLE_USE, 1.0f, 0.8f);
    }
    
    /**
     * Bắt đầu hiệu ứng Shrine of Maya
     */
    private void startShrineEffect(IllusoryHeartInstance instance) {
        BukkitTask task = new BukkitRunnable() {
            int ticks = 0;
            
            @Override
            public void run() {
                if (ticks >= instance.getDuration() || !instance.isActive()) {
                    // Kết thúc hiệu ứng
                    instance.deactivate();
                    activeDomes.remove(instance.getCaster().getUniqueId());

                    // Clear tất cả damage boost
                    clearAllDamageBoosts(instance.getCenter());

                    // Hiệu ứng kết thúc
                    createEndEffect(instance.getCenter());
                    instance.getCaster().sendMessage("§c[Illusory Heart] §7Shrine of Maya đã kết thúc!");
                    
                    this.cancel();
                    return;
                }
                
                // Tạo particle dome mỗi 4 ticks
                if (ticks % 4 == 0) {
                    createDomeParticles(instance);
                }
                
                // Áp dụng buff mỗi 20 ticks (1s)
                if (ticks % 20 == 0) {
                    applyBuffsBasedOnPlayerCount(instance);
                }
                
                // Hiệu ứng lightning mỗi 40 ticks (2s) nếu có 3+ players
                if (ticks % 40 == 0) {
                    applyLightningEffect(instance);
                }
                
                ticks++;
            }
        }.runTaskTimer(plugin, 0L, 1L);
        
        instance.setTask(task);
    }
    
    /**
     * Tạo dome particles đẹp với màu sắc thay đổi theo số lượng player
     */
    private void createDomeParticles(IllusoryHeartInstance instance) {
        Location center = instance.getCenter();
        Set<UUID> playersInside = getPlayersInDome(center);
        int playerCount = playersInside.size();

        // Màu sắc chính thay đổi theo số lượng player
        Color primaryColor = Color.fromRGB(144, 238, 144); // Xanh lá nhạt (cơ bản)
        Color accentColor = Color.fromRGB(255, 255, 255);  // Trắng

        if (playerCount >= 4) {
            // Xanh dương (nước) cho 4+ players
            primaryColor = Color.fromRGB(100, 149, 237);
            accentColor = Color.fromRGB(173, 216, 230);
        } else if (playerCount >= 3) {
            // Tím (sét) cho 3+ players
            primaryColor = Color.fromRGB(138, 43, 226);
            accentColor = Color.fromRGB(186, 85, 211);
        } else if (playerCount >= 2) {
            // Đỏ cam (lửa) cho 2+ players
            primaryColor = Color.fromRGB(255, 69, 0);
            accentColor = Color.fromRGB(255, 140, 0);
        }

        Particle.DustOptions primaryDust = new Particle.DustOptions(primaryColor, 1.0f);
        Particle.DustOptions accentDust = new Particle.DustOptions(accentColor, 1.2f);

        // Tạo dome chính - đẹp và rõ ràng hơn
        for (double phi = 0; phi <= Math.PI; phi += Math.PI / 25) {
            for (double theta = 0; theta <= 2 * Math.PI; theta += Math.PI / 25) {
                double x = DOME_RADIUS * Math.sin(phi) * Math.cos(theta);
                double y = DOME_RADIUS * Math.cos(phi);
                double z = DOME_RADIUS * Math.sin(phi) * Math.sin(theta);

                if (y >= -2) { // Chỉ hiển thị nửa trên
                    Location particleLocation = center.clone().add(x, y, z);

                    // Tạo dome với mật độ cao hơn
                    center.getWorld().spawnParticle(Particle.DUST, particleLocation, 1, primaryDust);

                    // Thêm highlights để dome nổi bật hơn
                    if (Math.random() < 0.15) {
                        center.getWorld().spawnParticle(Particle.DUST, particleLocation, 1, accentDust);
                    }
                }
            }
        }

        // Đỉnh dome đẹp với mandala
        createBeautifulApex(center, primaryColor, accentColor);

        // Vòng tròn đáy đẹp
        createBeautifulBase(center, primaryColor, accentColor);

        // Hiệu ứng bên trong nhẹ nhàng
        createSubtleInnerEffects(center, playerCount, primaryColor);
    }

    /**
     * Tạo đỉnh dome đẹp với mandala
     */
    private void createBeautifulApex(Location center, Color primaryColor, Color accentColor) {
        Location apex = center.clone().add(0, DOME_RADIUS, 0);

        // Viên ngọc trung tâm sáng
        center.getWorld().spawnParticle(Particle.DUST, apex, 1,
            new Particle.DustOptions(Color.fromRGB(255, 215, 0), 2.0f));

        // Vòng tròn mandala đơn giản
        for (int ring = 1; ring <= 2; ring++) {
            double radius = ring * 1.5;
            int points = ring * 8;

            for (int i = 0; i < points; i++) {
                double angle = (2 * Math.PI * i) / points;
                double x = radius * Math.cos(angle);
                double z = radius * Math.sin(angle);

                Location petalLocation = apex.clone().add(x, -0.3 * ring, z);
                center.getWorld().spawnParticle(Particle.DUST, petalLocation, 1,
                    new Particle.DustOptions(ring == 1 ? accentColor : primaryColor, 1.0f));
            }
        }

        // Tia sáng từ đỉnh xuống
        center.getWorld().spawnParticle(Particle.END_ROD, apex, 3, 0.3, 0.3, 0.3, 0.01);
    }

    /**
     * Tạo vòng tròn đáy đẹp
     */
    private void createBeautifulBase(Location center, Color primaryColor, Color accentColor) {
        // Vòng tròn chính
        for (double angle = 0; angle < 2 * Math.PI; angle += Math.PI / 32) {
            double x = DOME_RADIUS * Math.cos(angle);
            double z = DOME_RADIUS * Math.sin(angle);
            Location baseLocation = center.clone().add(x, 0, z);
            center.getWorld().spawnParticle(Particle.DUST, baseLocation, 1,
                new Particle.DustOptions(primaryColor, 1.0f));
        }

        // Các điểm sáng đặc biệt
        for (int i = 0; i < 8; i++) {
            double angle = (2 * Math.PI * i) / 8;
            double x = (DOME_RADIUS * 0.9) * Math.cos(angle);
            double z = (DOME_RADIUS * 0.9) * Math.sin(angle);
            Location accentLocation = center.clone().add(x, 0.2, z);
            center.getWorld().spawnParticle(Particle.ENCHANT, accentLocation, 2, 0.1, 0.1, 0.1, 0.01);
        }
    }

    /**
     * Tạo hiệu ứng bên trong nhẹ nhàng
     */
    private void createSubtleInnerEffects(Location center, int playerCount, Color primaryColor) {
        // Một vài hạt bụi sáng lơ lửng nhẹ nhàng
        for (int i = 0; i < 3; i++) {
            double x = (Math.random() - 0.5) * DOME_RADIUS * 0.8;
            double y = Math.random() * DOME_RADIUS * 0.6;
            double z = (Math.random() - 0.5) * DOME_RADIUS * 0.8;

            Location particleLocation = center.clone().add(x, y, z);

            if (isInsideDome(particleLocation, center)) {
                center.getWorld().spawnParticle(Particle.DUST, particleLocation, 1,
                    new Particle.DustOptions(primaryColor, 0.5f));
            }
        }

        // Hiệu ứng đặc biệt nhẹ theo số lượng player
        if (playerCount >= 2) {
            // Một chút lửa nhỏ
            double angle = Math.random() * 2 * Math.PI;
            double radius = Math.random() * DOME_RADIUS * 0.5;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);
            Location fireLocation = center.clone().add(x, 1, z);
            center.getWorld().spawnParticle(Particle.FLAME, fireLocation, 1, 0.1, 0.1, 0.1, 0.01);
        }

        if (playerCount >= 3) {
            // Một chút sét nhỏ
            double x = (Math.random() - 0.5) * DOME_RADIUS * 0.5;
            double y = Math.random() * DOME_RADIUS * 0.3;
            double z = (Math.random() - 0.5) * DOME_RADIUS * 0.5;
            Location lightningLocation = center.clone().add(x, y, z);
            center.getWorld().spawnParticle(Particle.ELECTRIC_SPARK, lightningLocation, 1, 0.1, 0.1, 0.1, 0.05);
        }
    }
    
    /**
     * Áp dụng buff dựa trên số lượng player trong dome
     */
    private void applyBuffsBasedOnPlayerCount(IllusoryHeartInstance instance) {
        Set<UUID> playersInside = getPlayersInDome(instance.getCenter());
        int playerCount = playersInside.size();

        if (playerCount < 2) {
            return; // Không có buff nếu chỉ có 1 người
        }

        for (UUID entityId : playersInside) {
            Player player = Bukkit.getPlayer(entityId);
            if (player == null || !player.isOnline()) continue;

            // 2+ players: Tăng sát thương
            if (playerCount >= 2) {
                applyDamageBoost(player, instance.getDamageBoost());
            }

            // 4+ players: Hồi máu và tăng tốc độ (chỉ cho caster)
            if (playerCount >= 4 && player.equals(instance.getCaster())) {
                applyHealingAndSpeed(player, instance.getHealPerSecond(), instance.getSpeedBoost());
            }
        }


    }

    /**
     * Áp dụng tăng sát thương
     */
    private void applyDamageBoost(Player player, double damageBoost) {
        // Lưu trữ thông tin tăng sát thương trong metadata với thời gian hết hạn
        long expireTime = System.currentTimeMillis() + 3000; // Hết hạn sau 3 giây
        player.setMetadata("illusory_heart_damage_boost",
            new org.bukkit.metadata.FixedMetadataValue(plugin, damageBoost));
        player.setMetadata("illusory_heart_damage_boost_expire",
            new org.bukkit.metadata.FixedMetadataValue(plugin, expireTime));

        // Hiệu ứng lửa nhỏ
        player.getWorld().spawnParticle(Particle.FLAME,
            player.getLocation().add(0, 1, 0), 3, 0.2, 0.2, 0.2, 0.01);
    }

    /**
     * Áp dụng hồi máu và tăng tốc độ
     */
    private void applyHealingAndSpeed(Player player, double healAmount, double speedBoost) {
        // Hồi máu
        double currentHealth = player.getHealth();
        double maxHealth = player.getMaxHealth();
        double newHealth = Math.min(maxHealth, currentHealth + healAmount);
        player.setHealth(newHealth);

        // Hiệu ứng hồi máu
        player.getWorld().spawnParticle(Particle.HEART,
            player.getLocation().add(0, 1.5, 0), 2, 0.2, 0.2, 0.2, 0.01);

        // Tăng tốc độ
        int amplifier = (int) Math.floor(speedBoost / 20.0); // 20% = level 0, 40% = level 1, etc.
        player.addPotionEffect(new PotionEffect(
            PotionEffectType.SPEED, 25, amplifier, false, false, true));

        // Hiệu ứng tăng tốc
        player.getWorld().spawnParticle(Particle.CLOUD,
            player.getLocation().add(0, 0.1, 0), 5, 0.2, 0.1, 0.2, 0.01);
    }

    /**
     * Clear tất cả damage boost của players trong khu vực
     */
    private void clearAllDamageBoosts(Location center) {
        for (Player player : center.getWorld().getPlayers()) {
            if (player.hasMetadata("illusory_heart_damage_boost")) {
                player.removeMetadata("illusory_heart_damage_boost", plugin);
                player.removeMetadata("illusory_heart_damage_boost_expire", plugin);
            }
        }
    }

    /**
     * Tạo hiệu ứng kết thúc
     */
    private void createEndEffect(Location center) {
        // Hiệu ứng âm thanh
        center.getWorld().playSound(center, Sound.BLOCK_BEACON_DEACTIVATE, 1.0f, 1.2f);

        // Hiệu ứng particle
        center.getWorld().spawnParticle(Particle.FLASH, center, 1, 0, 0, 0, 0);
        center.getWorld().spawnParticle(Particle.END_ROD, center, 20, 3, 3, 3, 0.1);
    }

    /**
     * Lấy danh sách player trong dome (bao gồm cả mobs để test)
     */
    private Set<UUID> getPlayersInDome(Location center) {
        Set<UUID> entitiesInside = new HashSet<>();

        // Đếm players
        for (Player player : center.getWorld().getPlayers()) {
            double distance = player.getLocation().distance(center);
            if (distance <= DOME_RADIUS && isInsideDome(player.getLocation(), center)) {
                entitiesInside.add(player.getUniqueId());
            }
        }

        // Đếm mobs để test (mỗi mob tính như 1 player)
        for (org.bukkit.entity.Entity entity : center.getWorld().getEntities()) {
            if (!(entity instanceof org.bukkit.entity.LivingEntity) || entity instanceof Player) {
                continue;
            }

            double distance = entity.getLocation().distance(center);
            if (distance <= DOME_RADIUS && isInsideDome(entity.getLocation(), center)) {
                entitiesInside.add(entity.getUniqueId());
            }
        }

        return entitiesInside;
    }

    /**
     * Kiểm tra xem vị trí có nằm trong dome không
     */
    private boolean isInsideDome(Location location, Location center) {
        double dx = location.getX() - center.getX();
        double dy = location.getY() - center.getY();
        double dz = location.getZ() - center.getZ();

        // Kiểm tra xem có nằm trong bán cầu không
        double distanceSquared = dx * dx + dy * dy + dz * dz;
        double radiusSquared = DOME_RADIUS * DOME_RADIUS;

        boolean isInSphere = distanceSquared <= radiusSquared;
        // Cho phép entities ở dưới center một chút (để mobs trên mặt đất có thể vào dome)
        boolean isAboveCenter = dy >= -2.0; // Cho phép 2 blocks dưới center

        return isInSphere && isAboveCenter;
    }

    /**
     * Kiểm tra xem player có được bảo vệ bởi protection plugins không
     */
    private boolean isPlayerProtected(Player target, Player attacker) {
        try {
            // Kiểm tra WorldGuard protection
            if (isWorldGuardProtected(target, attacker)) {
                return true;
            }

            // Kiểm tra Superior Skyblock protection
            if (isSuperiorSkyblockProtected(target, attacker)) {
                return true;
            }

        } catch (Exception e) {
            // Nếu có lỗi với protection plugins, cho phép damage
        }

        return false;
    }

    /**
     * Kiểm tra WorldGuard protection
     */
    private boolean isWorldGuardProtected(Player target, Player attacker) {
        try {
            // Kiểm tra nếu WorldGuard plugin tồn tại
            if (plugin.getServer().getPluginManager().getPlugin("WorldGuard") == null) {
                return false;
            }

            // Sử dụng reflection để kiểm tra WorldGuard protection
            // Tránh hard dependency
            Class<?> worldGuardClass = Class.forName("com.sk89q.worldguard.WorldGuard");
            Object instance = worldGuardClass.getMethod("getInstance").invoke(null);
            Object platformManager = worldGuardClass.getMethod("getPlatformManager").invoke(instance);

            Class<?> regionContainerClass = Class.forName("com.sk89q.worldguard.protection.regions.RegionContainer");
            Object regionContainer = platformManager.getClass().getMethod("getRegionContainer").invoke(platformManager);

            Class<?> bukkitAdapterClass = Class.forName("com.sk89q.worldedit.bukkit.BukkitAdapter");
            Object world = bukkitAdapterClass.getMethod("adapt", org.bukkit.World.class).invoke(null, target.getWorld());
            Object regionManager = regionContainerClass.getMethod("get", Class.forName("com.sk89q.worldedit.world.World")).invoke(regionContainer, world);

            if (regionManager != null) {
                Object location = bukkitAdapterClass.getMethod("adapt", org.bukkit.Location.class).invoke(null, target.getLocation());
                Class<?> regionQueryClass = Class.forName("com.sk89q.worldguard.protection.regions.RegionQuery");
                Object query = regionQueryClass.getMethod("createQuery").invoke(regionManager);

                // Kiểm tra PVP flag
                Class<?> flagsClass = Class.forName("com.sk89q.worldguard.protection.flags.Flags");
                Object pvpFlag = flagsClass.getField("PVP").get(null);
                Object result = query.getClass().getMethod("queryValue", Class.forName("com.sk89q.worldedit.util.Location"), Class.forName("com.sk89q.worldguard.protection.flags.Flag")).invoke(query, location, pvpFlag);

                if (result != null && result.equals(false)) {
                    return true; // PVP bị deny
                }
            }

        } catch (Exception e) {
            // Ignore reflection errors
        }

        return false;
    }

    /**
     * Kiểm tra Superior Skyblock protection
     */
    private boolean isSuperiorSkyblockProtected(Player target, Player attacker) {
        try {
            // Kiểm tra nếu SuperiorSkyblock plugin tồn tại
            if (plugin.getServer().getPluginManager().getPlugin("SuperiorSkyblock2") == null) {
                return false;
            }

            // Sử dụng SuperiorSkyblock API
            com.bgsoftware.superiorskyblock.api.island.Island island = com.bgsoftware.superiorskyblock.api.SuperiorSkyblockAPI.getGrid().getIslandAt(target.getLocation());

            if (island != null) {
                // Kiểm tra PVP setting của island
                try {
                    // Sử dụng reflection để tránh hard dependency
                    Class<?> islandFlagClass = Class.forName("com.bgsoftware.superiorskyblock.api.island.IslandFlag");
                    Object pvpFlag = islandFlagClass.getField("PVP").get(null);
                    boolean pvpEnabled = (Boolean) island.getClass().getMethod("hasSettingsEnabled", islandFlagClass).invoke(island, pvpFlag);
                    if (!pvpEnabled) {
                        return true; // PVP bị disable trên island
                    }
                } catch (Exception flagException) {
                    // Fallback: assume PVP is enabled if can't check
                }
            }

        } catch (Exception e) {
            // Ignore API errors
        }

        return false;
    }

    /**
     * Kiểm tra xem mob có được bảo vệ bởi protection plugins không
     */
    private boolean isMobProtected(org.bukkit.entity.LivingEntity mob, Player attacker) {
        try {
            // Kiểm tra WorldGuard mob-damage flag
            if (isWorldGuardMobProtected(mob, attacker)) {
                return true;
            }

            // Kiểm tra Superior Skyblock animal_damage permission
            if (isSuperiorSkyblockMobProtected(mob, attacker)) {
                return true;
            }

        } catch (Exception e) {
            // Nếu có lỗi với protection plugins, cho phép damage
        }

        return false;
    }

    /**
     * Kiểm tra WorldGuard mob-damage flag
     */
    private boolean isWorldGuardMobProtected(org.bukkit.entity.LivingEntity mob, Player attacker) {
        try {
            // Kiểm tra nếu WorldGuard plugin tồn tại
            if (plugin.getServer().getPluginManager().getPlugin("WorldGuard") == null) {
                return false;
            }

            // Sử dụng reflection để kiểm tra WorldGuard protection
            Class<?> worldGuardClass = Class.forName("com.sk89q.worldguard.WorldGuard");
            Object instance = worldGuardClass.getMethod("getInstance").invoke(null);
            Object platformManager = worldGuardClass.getMethod("getPlatformManager").invoke(instance);

            Class<?> regionContainerClass = Class.forName("com.sk89q.worldguard.protection.regions.RegionContainer");
            Object regionContainer = platformManager.getClass().getMethod("getRegionContainer").invoke(platformManager);

            Class<?> bukkitAdapterClass = Class.forName("com.sk89q.worldedit.bukkit.BukkitAdapter");
            Object world = bukkitAdapterClass.getMethod("adapt", org.bukkit.World.class).invoke(null, mob.getWorld());
            Object regionManager = regionContainerClass.getMethod("get", Class.forName("com.sk89q.worldedit.world.World")).invoke(regionContainer, world);

            if (regionManager != null) {
                Object location = bukkitAdapterClass.getMethod("adapt", org.bukkit.Location.class).invoke(null, mob.getLocation());
                Class<?> regionQueryClass = Class.forName("com.sk89q.worldguard.protection.regions.RegionQuery");
                Object query = regionQueryClass.getMethod("createQuery").invoke(regionManager);

                // Kiểm tra MOB_DAMAGE flag
                Class<?> flagsClass = Class.forName("com.sk89q.worldguard.protection.flags.Flags");
                Object mobDamageFlag = flagsClass.getField("MOB_DAMAGE").get(null);
                Object result = query.getClass().getMethod("queryValue", Class.forName("com.sk89q.worldedit.util.Location"), Class.forName("com.sk89q.worldguard.protection.flags.Flag")).invoke(query, location, mobDamageFlag);

                if (result != null && result.equals(false)) {
                    return true; // MOB_DAMAGE bị deny
                }
            }

        } catch (Exception e) {
            // Ignore reflection errors
        }

        return false;
    }

    /**
     * Kiểm tra Superior Skyblock animal_damage permission
     */
    private boolean isSuperiorSkyblockMobProtected(org.bukkit.entity.LivingEntity mob, Player attacker) {
        try {
            // Kiểm tra nếu SuperiorSkyblock plugin tồn tại
            if (plugin.getServer().getPluginManager().getPlugin("SuperiorSkyblock2") == null) {
                return false;
            }

            // Sử dụng SuperiorSkyblock API
            com.bgsoftware.superiorskyblock.api.island.Island island = com.bgsoftware.superiorskyblock.api.SuperiorSkyblockAPI.getGrid().getIslandAt(mob.getLocation());

            if (island != null) {
                // Lấy SuperiorPlayer của attacker
                com.bgsoftware.superiorskyblock.api.wrappers.SuperiorPlayer superiorPlayer = com.bgsoftware.superiorskyblock.api.SuperiorSkyblockAPI.getPlayer(attacker.getUniqueId());

                // Kiểm tra ANIMAL_DAMAGE permission
                try {
                    Class<?> islandPrivilegeClass = Class.forName("com.bgsoftware.superiorskyblock.api.island.IslandPrivilege");
                    Object animalDamagePrivilege = islandPrivilegeClass.getMethod("getByName", String.class).invoke(null, "ANIMAL_DAMAGE");
                    boolean hasPermission = (Boolean) island.getClass().getMethod("hasPermission", superiorPlayer.getClass(), islandPrivilegeClass).invoke(island, superiorPlayer, animalDamagePrivilege);

                    if (!hasPermission) {
                        return true; // Không có permission ANIMAL_DAMAGE
                    }
                } catch (Exception permException) {
                    // Fallback: assume permission granted if can't check
                }
            }

        } catch (Exception e) {
            // Ignore API errors
        }

        return false;
    }

    /**
     * Inner class để lưu trữ thông tin instance
     */
    private static class IllusoryHeartInstance {
        private final Player caster;
        private final Location center;
        private final double damageBoost;
        private final double lightningDamage;
        private final double healPerSecond;
        private final double speedBoost;
        private final int duration;
        private BukkitTask task;
        private boolean active = true;

        public IllusoryHeartInstance(Player caster, Location center, double damageBoost,
                                   double lightningDamage, double healPerSecond, double speedBoost, int duration) {
            this.caster = caster;
            this.center = center;
            this.damageBoost = damageBoost;
            this.lightningDamage = lightningDamage;
            this.healPerSecond = healPerSecond;
            this.speedBoost = speedBoost;
            this.duration = duration;
        }

        public Player getCaster() { return caster; }
        public Location getCenter() { return center; }
        public double getDamageBoost() { return damageBoost; }
        public double getLightningDamage() { return lightningDamage; }
        public double getHealPerSecond() { return healPerSecond; }
        public double getSpeedBoost() { return speedBoost; }
        public int getDuration() { return duration; }
        public boolean isActive() { return active; }
        public BukkitTask getTask() { return task; }
        public void setTask(BukkitTask task) { this.task = task; }
        public void deactivate() {
            active = false;
            if (task != null) task.cancel();
        }
    }

    /**
     * Áp dụng hiệu ứng sét đánh cho 3+ players (gây damage cho cả players và mobs)
     */
    private void applyLightningEffect(IllusoryHeartInstance instance) {
        Set<UUID> playersInside = getPlayersInDome(instance.getCenter());
        int entityCount = playersInside.size();

        if (entityCount < 3) {
            return;
        }

        // Gây damage cho tất cả players trong dome (trừ caster)
        for (UUID playerId : playersInside) {
            Player player = Bukkit.getPlayer(playerId);
            if (player == null || !player.isOnline()) continue;

            // Không gây damage cho caster
            if (player.equals(instance.getCaster())) continue;

            // Hiệu ứng sét đánh cho player
            player.getWorld().spawnParticle(Particle.ELECTRIC_SPARK,
                player.getLocation().add(0, 1, 0), 15, 0.5, 1.0, 0.5, 0.1);
            player.getWorld().playSound(player.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 0.8f, 1.5f);

            // Kiểm tra protection trước khi damage
            if (!isPlayerProtected(player, instance.getCaster())) {
                // Chỉ damage khi không được bảo vệ
                double currentHealth = player.getHealth();
                double newHealth = Math.max(0, currentHealth - instance.getLightningDamage());
                player.setHealth(newHealth);
            }


        }

        // Gây damage cho tất cả mobs trong dome
        for (org.bukkit.entity.Entity entity : instance.getCenter().getWorld().getEntities()) {
            if (!(entity instanceof org.bukkit.entity.LivingEntity) || entity instanceof Player) {
                continue;
            }

            org.bukkit.entity.LivingEntity mob = (org.bukkit.entity.LivingEntity) entity;
            double distance = mob.getLocation().distance(instance.getCenter());

            if (distance <= DOME_RADIUS && isInsideDome(mob.getLocation(), instance.getCenter())) {
                // Hiệu ứng sét đánh cho mob (luôn hiển thị)
                mob.getWorld().spawnParticle(Particle.ELECTRIC_SPARK,
                    mob.getLocation().add(0, 1, 0), 10, 0.5, 1.0, 0.5, 0.1);
                mob.getWorld().playSound(mob.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 0.5f, 1.5f);

                // Kiểm tra mob damage protection trước khi damage
                if (!isMobProtected(mob, instance.getCaster())) {
                    // Chỉ damage khi không được bảo vệ
                    double currentHealth = mob.getHealth();
                    double newHealth = Math.max(0, currentHealth - instance.getLightningDamage());
                    mob.setHealth(newHealth);
                }
            }
        }
    }
}
