package shyrcs.Ability;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Quản lý cooldown và hiển thị trên ActionBar
 * Tương tự như MMOItems với hiển thị liên tục
 */
public class CooldownManager {
    
    private static CooldownManager instance;
    private final Plugin plugin;
    
    // Map lưu trữ cooldown của từng player cho từng skill
    private final Map<UUID, Map<String, Long>> playerCooldowns = new ConcurrentHashMap<>();
    
    // Map lưu trữ thời gian cooldown cho từng skill type
    private final Map<String, Integer> skillCooldowns = new HashMap<>();

    // Task hiển thị buff time còn lại
    private BukkitRunnable buffTimeTask;

    // Track skill buffs để chỉ hiển thị buffs từ skills
    private final Map<UUID, Map<String, Long>> skillBuffs = new ConcurrentHashMap<>();
    private final Set<String> skillBuffTypes = new HashSet<>();
    
    private CooldownManager(Plugin plugin) {
        this.plugin = plugin;
        // Khởi tạo skill buff types
        initializeSkillBuffTypes();
        // Bắt đầu task theo dõi buff time
        startBuffTimeTask();
    }

    /**
     * Khởi tạo các loại buff từ skills
     */
    private void initializeSkillBuffTypes() {
        // Thêm các buff types từ skills vào set (đầy đủ, đồng bộ với BUFF_NAMES_VI)
        skillBuffTypes.add("ABSORPTION");
        skillBuffTypes.add("SPEED");
        skillBuffTypes.add("STRENGTH");
        skillBuffTypes.add("REGENERATION");
        skillBuffTypes.add("RESISTANCE");
        skillBuffTypes.add("FIRE_RESISTANCE");
        skillBuffTypes.add("WATER_BREATHING");
        skillBuffTypes.add("INVISIBILITY");
        skillBuffTypes.add("NIGHT_VISION");
        skillBuffTypes.add("HEALTH_BOOST");
        skillBuffTypes.add("SATURATION");
        skillBuffTypes.add("LUCK");
        skillBuffTypes.add("SLOW_FALLING");
        skillBuffTypes.add("CONDUIT_POWER");
        skillBuffTypes.add("DOLPHINS_GRACE");
        skillBuffTypes.add("HERO_OF_THE_VILLAGE");
        skillBuffTypes.add("HASTE");
        skillBuffTypes.add("JUMP_BOOST");
        skillBuffTypes.add("INSTANT_HEALTH");
        skillBuffTypes.add("GLOWING");

        // Debuff
        skillBuffTypes.add("SLOWNESS");
        skillBuffTypes.add("MINING_FATIGUE");
        skillBuffTypes.add("INSTANT_DAMAGE");
        skillBuffTypes.add("NAUSEA");
        skillBuffTypes.add("BLINDNESS");
        skillBuffTypes.add("HUNGER");
        skillBuffTypes.add("WEAKNESS");
        skillBuffTypes.add("POISON");
        skillBuffTypes.add("WITHER");
        skillBuffTypes.add("LEVITATION");
        skillBuffTypes.add("UNLUCK");
        skillBuffTypes.add("BAD_OMEN");
        skillBuffTypes.add("DARKNESS");
        skillBuffTypes.add("INFESTED");
        skillBuffTypes.add("OOZING");
        skillBuffTypes.add("WEAVING");
        skillBuffTypes.add("WIND_CHARGED");
        skillBuffTypes.add("RAID_OMEN");
        skillBuffTypes.add("TRIAL_OMEN");
        // Có thể thêm các buff khác từ skills
    }
    
    public static CooldownManager getInstance(Plugin plugin) {
        if (instance == null) {
            instance = new CooldownManager(plugin);
        }
        return instance;
    }
    
    public static CooldownManager getInstance() {
        return instance;
    }
    
    /**
     * Đặt cooldown cho player với skill cụ thể
     */
    public void setCooldown(Player player, String skillType, int cooldownSeconds) {
        UUID playerId = player.getUniqueId();
        long cooldownEnd = System.currentTimeMillis() + (cooldownSeconds * 1000L);
        
        playerCooldowns.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>())
                      .put(skillType, cooldownEnd);
        
        skillCooldowns.put(skillType, cooldownSeconds);
    }
    
    /**
     * Kiểm tra xem player có đang trong cooldown không
     */
    public boolean isOnCooldown(Player player, String skillType) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerSkillCooldowns = playerCooldowns.get(playerId);
        
        if (playerSkillCooldowns == null) {
            return false;
        }
        
        Long cooldownEnd = playerSkillCooldowns.get(skillType);
        if (cooldownEnd == null) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        if (currentTime >= cooldownEnd) {
            // Cooldown đã hết, xóa khỏi map
            playerSkillCooldowns.remove(skillType);
            if (playerSkillCooldowns.isEmpty()) {
                playerCooldowns.remove(playerId);
            }
            return false;
        }
        
        return true;
    }
    
    /**
     * Lấy thời gian cooldown còn lại (giây)
     */
    public long getRemainingCooldown(Player player, String skillType) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerSkillCooldowns = playerCooldowns.get(playerId);
        
        if (playerSkillCooldowns == null) {
            return 0;
        }
        
        Long cooldownEnd = playerSkillCooldowns.get(skillType);
        if (cooldownEnd == null) {
            return 0;
        }
        
        long currentTime = System.currentTimeMillis();
        long remaining = (cooldownEnd - currentTime) / 1000;
        
        return Math.max(0, remaining);
    }
    
    /**
     * Xóa tất cả cooldown của player
     */
    public void clearCooldowns(Player player) {
        playerCooldowns.remove(player.getUniqueId());
    }
    
    /**
     * Xóa cooldown cụ thể của player
     */
    public void clearCooldown(Player player, String skillType) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerSkillCooldowns = playerCooldowns.get(playerId);

        if (playerSkillCooldowns != null) {
            playerSkillCooldowns.remove(skillType);
            if (playerSkillCooldowns.isEmpty()) {
                playerCooldowns.remove(playerId);
            }
        }
    }

    /**
     * Track skill buff cho player
     */
    public void trackSkillBuff(Player player, String buffType, int durationSeconds) {
        UUID playerId = player.getUniqueId();
        long expireTime = System.currentTimeMillis() + (durationSeconds * 1000L);

        skillBuffs.computeIfAbsent(playerId, k -> new ConcurrentHashMap<>())
                  .put(buffType, expireTime);
    }

    /**
     * Xóa skill buff tracking
     */
    public void removeSkillBuff(Player player, String buffType) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerBuffs = skillBuffs.get(playerId);

        if (playerBuffs != null) {
            playerBuffs.remove(buffType);
            if (playerBuffs.isEmpty()) {
                skillBuffs.remove(playerId);
            }
        }
    }
    

    
    /**
     * Bắt đầu task theo dõi buff time còn lại (tối ưu)
     */
    private void startBuffTimeTask() {
        if (buffTimeTask != null) {
            buffTimeTask.cancel();
        }

        buffTimeTask = new BukkitRunnable() {
            @Override
            public void run() {
                // Chỉ check players có skill buffs đang hoạt động
                for (Player player : Bukkit.getOnlinePlayers()) {
                    checkSkillBuffsOnly(player);
                }
            }
        };

        // Tăng interval lên 20 ticks (1 giây) để giảm tải
        buffTimeTask.runTaskTimer(plugin, 0L, 20L);
    }

    /**
     * Kiểm tra và hiển thị buff time còn lại nếu < 3 giây
     */
    private void checkAndDisplayBuffTime(Player player) {
        StringBuilder actionBarText = new StringBuilder();
        boolean hasShortBuff = false;

        // Kiểm tra tất cả potion effects của player
        for (PotionEffect effect : player.getActivePotionEffects()) {
            int remainingSeconds = effect.getDuration() / 20; // Chuyển từ tick sang giây

            // Chỉ hiển thị buff còn dưới 3 giây và là buff tích cực
            if (remainingSeconds <= 3 && remainingSeconds > 0 && BuffUtils.isPositiveEffect(effect.getType())) {
                if (hasShortBuff) {
                    actionBarText.append(" §7| ");
                }

                String buffNameVi = BuffPlaceholderUtil.getBuffNameVi(effect.getType());
                String levelRoman = BuffUtils.toRoman(effect.getAmplifier() + 1);
                actionBarText.append("§e").append(buffNameVi).append(" ").append(levelRoman)
                           .append(": §c").append(remainingSeconds).append("s");
                hasShortBuff = true;
            }
        }

        if (hasShortBuff) {
            BuffUtils.sendActionBar(player, actionBarText.toString());
        }
    }

    /**
     * Chỉ kiểm tra skill buffs sắp hết thời gian (tối ưu performance)
     */
    private void checkSkillBuffsOnly(Player player) {
        UUID playerId = player.getUniqueId();
        Map<String, Long> playerBuffs = skillBuffs.get(playerId);

        if (playerBuffs == null || playerBuffs.isEmpty()) {
            return;
        }

        StringBuilder actionBarText = new StringBuilder();
        boolean hasShortBuff = false;
        long currentTime = System.currentTimeMillis();

        // Chỉ kiểm tra buffs được track từ skills
        for (Map.Entry<String, Long> entry : playerBuffs.entrySet()) {
            String buffType = entry.getKey();
            long expireTime = entry.getValue();

            long remainingMs = expireTime - currentTime;
            int remainingSeconds = (int) (remainingMs / 1000);

            // Chỉ hiển thị buff còn dưới 3 giây
            if (remainingSeconds <= 3 && remainingSeconds > 0) {
                // Verify player vẫn có buff này
                try {
                    org.bukkit.NamespacedKey key = org.bukkit.NamespacedKey.minecraft(buffType.toLowerCase());
                    org.bukkit.potion.PotionEffectType effectType = org.bukkit.Registry.EFFECT.get(key);
                    if (effectType != null) {
                        PotionEffect effect = player.getPotionEffect(effectType);
                        if (effect != null) {
                            if (hasShortBuff) {
                                actionBarText.append(" §7| ");
                            }

                            String buffNameVi = BuffPlaceholderUtil.getBuffNameVi(effect.getType());
                            String levelRoman = BuffUtils.toRoman(effect.getAmplifier() + 1);
                            actionBarText.append("§e").append(buffNameVi).append(" ").append(levelRoman)
                                       .append(": §c").append(remainingSeconds).append("s");
                            hasShortBuff = true;
                        } else {
                            // Buff không còn, xóa khỏi tracking
                            playerBuffs.remove(buffType);
                        }
                    }
                } catch (Exception e) {
                    // Fallback: xóa buff type không hợp lệ
                    playerBuffs.remove(buffType);
                }
            } else if (remainingSeconds <= 0) {
                // Buff đã hết, xóa khỏi tracking
                playerBuffs.remove(buffType);
            }
        }

        // Clean up empty player buffs
        if (playerBuffs.isEmpty()) {
            skillBuffs.remove(playerId);
        }

        // Hiển thị action bar nếu có buff sắp hết
        if (hasShortBuff) {
            BuffUtils.sendActionBar(player, actionBarText.toString());
        }
    }

    /**
     * Dừng CooldownManager
     */
    public void shutdown() {
        if (buffTimeTask != null) {
            buffTimeTask.cancel();
            buffTimeTask = null;
        }
        playerCooldowns.clear();
        skillCooldowns.clear();
    }
    
    /**
     * Lấy tất cả cooldown hiện tại của player (cho debug)
     */
    public Map<String, Long> getPlayerCooldowns(Player player) {
        Map<String, Long> cooldowns = playerCooldowns.get(player.getUniqueId());
        return cooldowns != null ? new HashMap<>(cooldowns) : new HashMap<>();
    }
}
