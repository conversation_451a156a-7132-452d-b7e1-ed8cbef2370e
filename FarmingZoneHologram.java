package shyrcs.Skills;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

// HeadDatabase API
import me.arcaniax.hdb.api.HeadDatabaseAPI;

/**
 * Class quản lý Hologram Head cho Farming Zone
 */
public class FarmingZoneHologram {

    private final Plugin plugin;
    private final Player owner;
    private final Location fixedLocation; // Vị trí cố định
    private ArmorStand hologramEntity;
    private BukkitTask rotationTask; // Task để xoay
    
    // HeadDatabase head ID for farming zone
    private static final String HEAD_ID = "18260"; // Your farming head ID
    
    public FarmingZoneHologram(Plugin plugin, Player owner) {
        this.plugin = plugin;
        this.owner = owner;
        // Tạo vị trí cố định 8 blocks phía trên vị trí hiện tại của player
        this.fixedLocation = owner.getLocation().clone().add(0, 8, 0);
        createHologram();
        startRotationTask();
    }
    
    /**
     * Tạo hologram armor stand với custom head tại vị trí cố định
     */
    private void createHologram() {
        // Sử dụng vị trí cố định đã tính toán (6 blocks phía trên)
        Location hologramLocation = fixedLocation.clone();
        
        // Tạo armor stand
        hologramEntity = (ArmorStand) owner.getWorld().spawnEntity(hologramLocation, EntityType.ARMOR_STAND);
        
        // Cấu hình armor stand
        hologramEntity.setVisible(false); // Invisible body
        hologramEntity.setGravity(false); // No gravity
        hologramEntity.setCanPickupItems(false);
        hologramEntity.setRemoveWhenFarAway(false);
        hologramEntity.setInvulnerable(true);
        hologramEntity.setMarker(true); // Marker mode
        hologramEntity.setSmall(false);
        hologramEntity.setBasePlate(false);
        hologramEntity.setArms(false);
        
        // Tạo custom head với texture
        ItemStack customHead = createCustomHead();
        hologramEntity.getEquipment().setHelmet(customHead);

        // Remove custom name - quá thấp
        // hologramEntity.customName(net.kyori.adventure.text.Component.text("§a§lFarming Zone"));
        // hologramEntity.setCustomNameVisible(true);
    }
    
    /**
     * Tạo custom head với texture
     */
    private ItemStack createCustomHead() {
        try {
            // Try to use HeadDatabase API first
            if (Bukkit.getPluginManager().getPlugin("HeadDatabase") != null) {
                HeadDatabaseAPI api = new HeadDatabaseAPI();

                // Get head by ID using HeadDatabase API
                ItemStack customHead = api.getItemHead(HEAD_ID);

                if (customHead != null) {
                    // Set custom display name
                    if (customHead.hasItemMeta() && customHead.getItemMeta() != null) {
                        var meta = customHead.getItemMeta();
                        meta.displayName(net.kyori.adventure.text.Component.text("§a§lFarming Zone Head"));
                        customHead.setItemMeta(meta);
                    }

                    plugin.getLogger().info("Created custom head using HeadDatabase API with ID: " + HEAD_ID);
                    return customHead;
                } else {
                    plugin.getLogger().warning("HeadDatabase returned null for head ID: " + HEAD_ID);
                }
            } else {
                plugin.getLogger().info("HeadDatabase plugin not found");
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create custom head with HeadDatabase: " + e.getMessage());
        }

        // Fallback: use player head
        plugin.getLogger().info("Using player head as fallback for farming zone hologram");
        ItemStack head = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta) head.getItemMeta();

        if (meta != null) {
            meta.setOwningPlayer(owner);
            meta.displayName(net.kyori.adventure.text.Component.text("§a§lFarming Zone Head"));
            head.setItemMeta(meta);
        }

        return head;
    }






    /**
     * Bắt đầu task để hologram xoay tròn tại vị trí cố định
     */
    private void startRotationTask() {
        rotationTask = new BukkitRunnable() {
            private float yaw = 0.0f;

            @Override
            public void run() {
                if (hologramEntity == null || hologramEntity.isDead() || !owner.isOnline()) {
                    this.cancel();
                    return;
                }

                // Tăng yaw để tạo hiệu ứng xoay tròn
                yaw += 5.0f; // Xoay 5 độ mỗi tick
                if (yaw >= 360.0f) {
                    yaw = 0.0f;
                }

                // Tạo location mới với yaw đã cập nhật
                Location rotatedLocation = fixedLocation.clone();
                rotatedLocation.setYaw(yaw);

                // Teleport hologram với rotation mới
                hologramEntity.teleport(rotatedLocation);
            }
        }.runTaskTimer(plugin, 0L, 2L); // Update every 2 ticks (0.1s) cho smooth rotation
    }
    
    /**
     * Lấy vị trí hiện tại của hologram (vị trí cố định)
     */
    public Location getLocation() {
        return fixedLocation.clone();
    }
    
    /**
     * Lấy player owner
     */
    public Player getOwner() {
        return owner;
    }
    
    /**
     * Kiểm tra hologram có còn valid không
     */
    public boolean isValid() {
        return hologramEntity != null && !hologramEntity.isDead() && owner.isOnline();
    }
    
    /**
     * Lấy ArmorStand entity
     */
    public ArmorStand getEntity() {
        return hologramEntity;
    }
    
    /**
     * Cleanup hologram
     */
    public void remove() {
        // Cancel rotation task
        if (rotationTask != null && !rotationTask.isCancelled()) {
            rotationTask.cancel();
        }

        // Remove armor stand
        if (hologramEntity != null && !hologramEntity.isDead()) {
            hologramEntity.remove();
        }

        hologramEntity = null;
        rotationTask = null;
    }
}
