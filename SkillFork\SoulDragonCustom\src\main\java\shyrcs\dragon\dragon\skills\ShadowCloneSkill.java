package shyrcs.dragon.dragon.skills;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.CustomDragon;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Shadow Clone skill - Creates invisible shadow clones with particle effects
 */
public class ShadowCloneSkill extends DragonSkill {
    
    private final Random random = new Random();
    private final List<ShadowClone> shadowClones = new ArrayList<>();
    
    public ShadowCloneSkill(SoulDragonPlugin plugin, CustomDragon customDragon) {
        super(plugin, customDragon, "shadow_clone");
    }
    
    @Override
    public boolean canUse() {
        // Only use when there are no existing clones and dragon health is below 50%
        double healthPercentage = customDragon.getDragon().getHealth() / customDragon.getDragon().getMaxHealth();
        return shadowClones.isEmpty() && healthPercentage < 0.5;
    }
    
    @Override
    public void use() {
        Location dragonLocation = customDragon.getDragon().getLocation();
        
        // Play sound
        dragonLocation.getWorld().playSound(dragonLocation, Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 0.3f);
        dragonLocation.getWorld().playSound(dragonLocation, Sound.ENTITY_ENDERMAN_TELEPORT, 1.5f, 0.5f);
        
        // Create 1-2 shadow clones
        int cloneCount = 1 + random.nextInt(2);
        
        for (int i = 0; i < cloneCount; i++) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    createShadowClone();
                }
            }.runTaskLater(plugin, i * 20L); // Spawn one every second
        }
    }
    
    /**
     * Create a shadow clone
     */
    private void createShadowClone() {
        Location dragonLocation = customDragon.getDragon().getLocation();
        
        // Calculate random position around dragon
        double angle = random.nextDouble() * 2 * Math.PI;
        double distance = 15 + random.nextDouble() * 10; // 15-25 blocks away
        double height = random.nextDouble() * 10 - 5; // Height variation
        
        Location cloneLocation = dragonLocation.clone().add(
            Math.cos(angle) * distance,
            height,
            Math.sin(angle) * distance
        );
        
        try {
            // Create invisible armor stand as clone marker
            ArmorStand cloneMarker = (ArmorStand) cloneLocation.getWorld().spawnEntity(cloneLocation, EntityType.ARMOR_STAND);
            cloneMarker.setVisible(false);
            cloneMarker.setGravity(false);
            cloneMarker.setCanPickupItems(false);
            cloneMarker.setInvulnerable(true);
            cloneMarker.setMarker(true);
            cloneMarker.setSmall(true);
            
            // Create shadow clone
            ShadowClone clone = new ShadowClone(cloneMarker, cloneLocation);
            shadowClones.add(clone);
            
            // Start clone behavior
            clone.startBehavior();
            
            // Spawn effect
            cloneLocation.getWorld().spawnParticle(
                Particle.PORTAL,
                cloneLocation,
                30,
                2.0, 2.0, 2.0,
                0.1
            );
            
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to create shadow clone: " + e.getMessage());
        }
    }
    
    @Override
    public int getCooldownSeconds() {
        return 45; // Long cooldown for deception skill
    }
    
    @Override
    public void cleanup() {
        for (ShadowClone clone : shadowClones) {
            clone.destroy();
        }
        shadowClones.clear();
    }
    
    /**
     * Shadow clone class
     */
    private class ShadowClone {
        private final ArmorStand marker;
        private final Location baseLocation;
        private BukkitRunnable behaviorTask;
        private int lifeTicks;
        private final int maxLifeTicks = 600; // 30 seconds
        
        public ShadowClone(ArmorStand marker, Location baseLocation) {
            this.marker = marker;
            this.baseLocation = baseLocation.clone();
            this.lifeTicks = 0;
        }
        
        /**
         * Start clone behavior
         */
        public void startBehavior() {
            behaviorTask = new BukkitRunnable() {
                @Override
                public void run() {
                    lifeTicks++;
                    
                    if (lifeTicks >= maxLifeTicks || marker.isDead()) {
                        destroy();
                        this.cancel();
                        return;
                    }
                    
                    // Update clone behavior
                    updateBehavior();
                }
            };
            behaviorTask.runTaskTimer(plugin, 0L, 1L);
        }
        
        /**
         * Update clone behavior
         */
        private void updateBehavior() {
            // Move clone randomly
            if (lifeTicks % 40 == 0) { // Every 2 seconds
                double moveX = (random.nextDouble() - 0.5) * 4;
                double moveY = (random.nextDouble() - 0.5) * 2;
                double moveZ = (random.nextDouble() - 0.5) * 4;
                
                Location newLocation = marker.getLocation().add(moveX, moveY, moveZ);
                
                // Keep within reasonable distance from base
                if (newLocation.distance(baseLocation) < 20) {
                    marker.teleport(newLocation);
                }
            }
            
            // Spawn shadow particles
            if (lifeTicks % 5 == 0) { // Every 0.25 seconds
                Location particleLocation = marker.getLocation();
                
                particleLocation.getWorld().spawnParticle(
                    Particle.SMOKE,
                    particleLocation,
                    3,
                    1.0, 1.0, 1.0,
                    0.05
                );
                
                particleLocation.getWorld().spawnParticle(
                    Particle.ASH,
                    particleLocation,
                    2,
                    0.5, 0.5, 0.5,
                    0.02
                );
            }
            
            // Occasional "attack" particles to confuse players
            if (lifeTicks % 60 == 0 && random.nextDouble() < 0.3) { // Every 3 seconds, 30% chance
                Location attackLocation = marker.getLocation();
                
                attackLocation.getWorld().spawnParticle(
                    Particle.FLAME,
                    attackLocation,
                    10,
                    2.0, 2.0, 2.0,
                    0.1
                );
                
                attackLocation.getWorld().playSound(attackLocation, Sound.ENTITY_ENDER_DRAGON_SHOOT, 0.5f, 0.8f);
            }
            
            // Fade out effect near end of life
            if (lifeTicks > maxLifeTicks - 60) { // Last 3 seconds
                Location fadeLocation = marker.getLocation();
                
                fadeLocation.getWorld().spawnParticle(
                    Particle.PORTAL,
                    fadeLocation,
                    5,
                    1.0, 1.0, 1.0,
                    0.1
                );
            }
        }
        
        /**
         * Destroy the clone
         */
        public void destroy() {
            if (behaviorTask != null && !behaviorTask.isCancelled()) {
                behaviorTask.cancel();
            }
            
            if (marker != null && !marker.isDead()) {
                Location destroyLocation = marker.getLocation();
                
                // Destruction effect
                destroyLocation.getWorld().spawnParticle(
                    Particle.PORTAL,
                    destroyLocation,
                    20,
                    2.0, 2.0, 2.0,
                    0.1
                );
                
                destroyLocation.getWorld().playSound(destroyLocation, Sound.ENTITY_ENDERMAN_TELEPORT, 1.0f, 0.5f);
                
                marker.remove();
            }
            
            shadowClones.remove(this);
        }
    }
}
