package shyrcs.Skills;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import shyrcs.Ability.CooldownManager;

// WorldGuard API imports
import com.sk89q.worldguard.WorldGuard;
import com.sk89q.worldguard.bukkit.WorldGuardPlugin;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.Flags;
import com.sk89q.worldguard.protection.managers.RegionManager;
import com.sk89q.worldguard.protection.regions.RegionContainer;
import com.sk89q.worldedit.bukkit.BukkitAdapter;
import com.sk89q.worldedit.math.BlockVector3;

// SuperiorSkyblock API imports
import com.bgsoftware.superiorskyblock.api.SuperiorSkyblockAPI;
import com.bgsoftware.superiorskyblock.api.island.Island;
import com.bgsoftware.superiorskyblock.api.wrappers.SuperiorPlayer;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class DeathZoneListener implements Listener {

    private final Plugin plugin;
    private final DeathZoneEffect deathZoneEffect;
    private final CooldownManager cooldownManager;
    private final Map<UUID, Long> cooldowns = new HashMap<>();

    public DeathZoneListener(Plugin plugin) {
        this.plugin = plugin;
        this.deathZoneEffect = new DeathZoneEffect(plugin);
        this.cooldownManager = CooldownManager.getInstance(plugin);
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();

        if (item == null) return;

        // Kiểm tra action
        Action action = event.getAction();
        if (action != Action.RIGHT_CLICK_AIR && action != Action.RIGHT_CLICK_BLOCK &&
            action != Action.LEFT_CLICK_AIR && action != Action.LEFT_CLICK_BLOCK) {
            return;
        }

        // Kiểm tra NBT
        NBTItem nbtItem = NBTItem.get(item);
        String deathZoneConfig = findDeathZoneConfig(nbtItem);

        if (deathZoneConfig == null) {
            return;
        }

        event.setCancelled(true);

        // Parse config: death_zone <dmg> <time> <click> <cooldown>
        String[] parts = deathZoneConfig.split(" ");
        if (parts.length < 5) {
            player.sendMessage("§c[Death Zone] §7Invalid skill configuration!");
            return;
        }

        try {
            double damage = Double.parseDouble(parts[1]);
            int duration = Integer.parseInt(parts[2]);
            String requiredClick = parts[3].toLowerCase();
            int cooldownSeconds = Integer.parseInt(parts[4]);

            // Kiểm tra click type
            boolean isRightClick = action == Action.RIGHT_CLICK_AIR || action == Action.RIGHT_CLICK_BLOCK;
            boolean isLeftClick = action == Action.LEFT_CLICK_AIR || action == Action.LEFT_CLICK_BLOCK;

            if ((requiredClick.equals("right_click") && !isRightClick) ||
                (requiredClick.equals("left_click") && !isLeftClick)) {
                return;
            }

            // Kiểm tra cooldown
            if (cooldownManager.isOnCooldown(player, "death_zone")) {
                long remaining = cooldownManager.getRemainingCooldown(player, "death_zone");
                player.sendMessage("§c[Death Zone] §7Cooldown: " + remaining + "s");
                return;
            }

            // Kiểm tra permissions
            if (!canUseDeathZone(player, player.getLocation())) {
                player.sendMessage("§c[Death Zone] §7Không thể sử dụng Death Zone tại khu vực được bảo vệ!");
                return;
            }

            // Set cooldown
            cooldownManager.setCooldown(player, "death_zone", cooldownSeconds);

            // Kích hoạt Death Zone
            deathZoneEffect.activateDeathZone(player, damage, duration);

        } catch (NumberFormatException e) {
            player.sendMessage("§c[Death Zone] §7Invalid skill configuration!");
        }
    }

    /**
     * Tìm death_zone config trong NBT tags của item
     * Format mới: soulskills = "death_zone <dmg> <time> <click> <cooldown>"
     */
    private String findDeathZoneConfig(NBTItem nbtItem) {
        // Kiểm tra tag "soulskills" trước (format mới)
        if (nbtItem.hasTag("soulskills")) {
            String value = nbtItem.getString("soulskills");
            if (value != null && value.startsWith("death_zone")) {
                return value;
            }
        }

        // Fallback: kiểm tra các tag cũ để backward compatibility
        String[] possibleTags = {
            "MMOITEMS_DEATH_ZONE",
            "death_zone", 
            "DEATH_ZONE",
            "skill_death_zone",
            "SKILL_DEATH_ZONE"
        };

        for (String tag : possibleTags) {
            if (nbtItem.hasTag(tag)) {
                String value = nbtItem.getString(tag);
                if (value != null && value.startsWith("death_zone")) {
                    return value;
                }
            }
        }

        // Fallback search trong tất cả tags
        for (String key : nbtItem.getTags()) {
            if (key.toLowerCase().contains("death") || key.toLowerCase().contains("zone")) {
                String value = nbtItem.getString(key);
                if (value != null && value.startsWith("death_zone")) {
                    return value;
                }
            }
        }

        return null;
    }

    /**
     * Kiểm tra xem player có thể sử dụng Death Zone tại location không
     */
    private boolean canUseDeathZone(Player player, Location location) {
        // Kiểm tra WorldGuard với proper API
        if (!canUseWorldGuard(player, location)) {
            return false;
        }

        // Kiểm tra Superior Skyblock với proper API
        if (!canUseSuperiorSkyblock(player, location)) {
            return false;
        }

        return true;
    }

    /**
     * Kiểm tra WorldGuard permissions với proper API
     */
    private boolean canUseWorldGuard(Player player, Location location) {
        try {
            // Kiểm tra xem WorldGuard có tồn tại không
            if (plugin.getServer().getPluginManager().getPlugin("WorldGuard") == null) {
                return true; // Không có WorldGuard = cho phép
            }

            // Sử dụng WorldGuard API
            RegionContainer container = WorldGuard.getInstance().getPlatform().getRegionContainer();
            RegionManager regions = container.get(BukkitAdapter.adapt(location.getWorld()));

            if (regions == null) {
                return true; // Không có region manager = cho phép
            }

            // Convert location to BlockVector3
            BlockVector3 blockVector = BukkitAdapter.asBlockVector(location);

            // Get applicable regions
            ApplicableRegionSet applicableRegions = regions.getApplicableRegions(blockVector);

            // Convert player to WorldGuard player
            com.sk89q.worldguard.LocalPlayer localPlayer = WorldGuardPlugin.inst().wrapPlayer(player);

            // Test PVP flag - returns true if ALLOW, false if DENY
            Boolean pvpAllowed = applicableRegions.testState(localPlayer, Flags.PVP);

            // If PVP is explicitly denied, block Death Zone
            if (pvpAllowed != null && !pvpAllowed) {
                return false;
            }

            // PVP allowed or not set - allow Death Zone
            return true;

        } catch (Exception e) {
            plugin.getLogger().warning("Error checking WorldGuard permissions: " + e.getMessage());
            return false; // Safe fallback
        }
    }

    /**
     * Kiểm tra Superior Skyblock permissions với proper API
     */
    private boolean canUseSuperiorSkyblock(Player player, Location location) {
        try {
            // Kiểm tra xem SuperiorSkyblock có tồn tại không
            if (plugin.getServer().getPluginManager().getPlugin("SuperiorSkyblock2") == null) {
                return true; // Không có SuperiorSkyblock = cho phép
            }

            // Sử dụng SuperiorSkyblock API
            Island island = SuperiorSkyblockAPI.getGrid().getIslandAt(location);

            if (island == null) {
                return true; // Không có island = cho phép (wilderness)
            }

            // Kiểm tra xem player có phải member của island không
            SuperiorPlayer superiorPlayer = SuperiorSkyblockAPI.getPlayer(player.getUniqueId());

            boolean isMember = island.isMember(superiorPlayer);



            return isMember; // Chỉ cho phép nếu là member của island

        } catch (Exception e) {
            plugin.getLogger().warning("Error checking SuperiorSkyblock permissions: " + e.getMessage());
            return true; // Fallback - allow if error
        }
    }

    /**
     * Cleanup khi plugin disable
     */
    public void cleanup() {
        deathZoneEffect.cleanup();
        cooldowns.clear();
    }
}
