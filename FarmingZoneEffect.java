package shyrcs.Skills;

import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.data.Ageable;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

// WorldGuard API imports
import com.sk89q.worldguard.WorldGuard;
import com.sk89q.worldguard.bukkit.WorldGuardPlugin;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.flags.Flags;
import com.sk89q.worldguard.protection.managers.RegionManager;
import com.sk89q.worldguard.protection.regions.RegionContainer;
import com.sk89q.worldedit.bukkit.BukkitAdapter;
import com.sk89q.worldedit.math.BlockVector3;

// SuperiorSkyblock API imports
import com.bgsoftware.superiorskyblock.api.SuperiorSkyblockAPI;
import com.bgsoftware.superiorskyblock.api.island.Island;
import com.bgsoftware.superiorskyblock.api.wrappers.SuperiorPlayer;

import java.util.*;

/**
 * Class xử lý logic Farming Zone effect
 */
public class FarmingZoneEffect {

    private static final int CROPS_PER_SECOND = 5; // 5 cây/giây
    private static final int TICKS_PER_CROP = 20 / CROPS_PER_SECOND; // 4 ticks per crop
    
    private final Plugin plugin;
    private final Map<UUID, FarmingZoneInstance> activeFarmingZones = new HashMap<>();
    
    // Supported crops
    private static final Set<Material> FARMABLE_CROPS = Set.of(
        Material.WHEAT, Material.CARROTS, Material.POTATOES, Material.BEETROOTS,
        Material.NETHER_WART, Material.COCOA, Material.SWEET_BERRY_BUSH
    );
    
    // Crop to replant block mapping (block materials only)
    private static final Map<Material, Material> CROP_TO_REPLANT_BLOCK = Map.of(
        Material.WHEAT, Material.WHEAT,
        Material.CARROTS, Material.CARROTS,
        Material.POTATOES, Material.POTATOES,
        Material.BEETROOTS, Material.BEETROOTS,
        Material.NETHER_WART, Material.NETHER_WART,
        Material.SWEET_BERRY_BUSH, Material.SWEET_BERRY_BUSH
    );
    
    public FarmingZoneEffect(Plugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Kích hoạt Farming Zone với custom range
     */
    public void activateFarmingZone(Player player, double farmingRadius, int durationSeconds) {
        UUID playerId = player.getUniqueId();

        // Kiểm tra bảo vệ trước khi kích hoạt
        if (!canUseFarmingZone(player, player.getLocation())) {

            return;
        }

        // Tạo hologram
        FarmingZoneHologram hologram = new FarmingZoneHologram(plugin, player);
        
        // Tạo farming zone instance với custom range
        FarmingZoneInstance instance = new FarmingZoneInstance(player, hologram, farmingRadius, durationSeconds);
        activeFarmingZones.put(playerId, instance);

        // Bắt đầu farming task
        startFarmingTask(instance);


    }

    /**
     * Kiểm tra xem player có farming zone đang active không
     */
    public boolean hasActiveFarmingZone(UUID playerId) {
        FarmingZoneInstance instance = activeFarmingZones.get(playerId);
        return instance != null && instance.isActive();
    }

    /**
     * Kiểm tra xem player có thể sử dụng Farming Zone tại location không
     */
    private boolean canUseFarmingZone(Player player, Location location) {
        // Kiểm tra WorldGuard với proper API
        if (!canUseWorldGuard(player, location)) {
            return false;
        }

        // Kiểm tra Superior Skyblock
        if (!canUseSuperiorSkyblock(player, location)) {
            return false;
        }

        return true;
    }

    /**
     * Kiểm tra WorldGuard permissions với proper API
     */
    private boolean canUseWorldGuard(Player player, Location location) {
        try {
            // Kiểm tra xem WorldGuard có tồn tại không
            if (Bukkit.getPluginManager().getPlugin("WorldGuard") == null) {
                return true; // Không có WorldGuard = cho phép
            }

            // Sử dụng WorldGuard API
            RegionContainer container = WorldGuard.getInstance().getPlatform().getRegionContainer();
            RegionManager regions = container.get(BukkitAdapter.adapt(location.getWorld()));

            if (regions == null) {
                return true; // Không có region manager = cho phép
            }

            // Convert location to BlockVector3
            BlockVector3 blockVector = BukkitAdapter.asBlockVector(location);

            // Get applicable regions
            ApplicableRegionSet applicableRegions = regions.getApplicableRegions(blockVector);

            // Test BLOCK_BREAK flag
            Boolean blockBreakAllowed;

            if (player != null) {
                // Convert player to WorldGuard player
                com.sk89q.worldguard.LocalPlayer localPlayer = WorldGuardPlugin.inst().wrapPlayer(player);
                blockBreakAllowed = applicableRegions.testState(localPlayer, Flags.BLOCK_BREAK);

                // Debug removed - plugin working
            } else {
                // No player context - check global flag
                com.sk89q.worldguard.protection.flags.StateFlag.State state = applicableRegions.queryState(null, Flags.BLOCK_BREAK);
                blockBreakAllowed = (state == com.sk89q.worldguard.protection.flags.StateFlag.State.ALLOW);

                // Debug removed - plugin working
            }

            // If BLOCK_BREAK is explicitly denied, block Farming Zone
            if (blockBreakAllowed != null && !blockBreakAllowed) {
                return false;
            }

            // BLOCK_BREAK allowed or not set - allow Farming Zone
            return true;

        } catch (Exception e) {
            plugin.getLogger().warning("Error checking WorldGuard permissions: " + e.getClass().getSimpleName() + " - " + e.getMessage());
            return false; // Safe fallback
        }
    }

    /**
     * Kiểm tra Superior Skyblock permissions với proper API
     */
    private boolean canUseSuperiorSkyblock(Player player, Location location) {
        try {
            // Kiểm tra xem SuperiorSkyblock có tồn tại không
            if (Bukkit.getPluginManager().getPlugin("SuperiorSkyblock2") == null) {
                return true; // Không có SuperiorSkyblock = cho phép
            }

            // Sử dụng SuperiorSkyblock API
            Island island = SuperiorSkyblockAPI.getGrid().getIslandAt(location);

            if (island == null) {
                return true; // Không có island = cho phép (wilderness)
            }

            // Kiểm tra xem player có phải member của island không
            SuperiorPlayer superiorPlayer = SuperiorSkyblockAPI.getPlayer(player.getUniqueId());

            boolean isMember = island.isMember(superiorPlayer);

            plugin.getLogger().info("Farming Zone SuperiorSkyblock check: isMember=" + isMember +
                                   " for player " + player.getName() + " on island " + island.getName());

            return isMember; // Chỉ cho phép nếu là member của island

        } catch (Exception e) {
            plugin.getLogger().warning("Error checking SuperiorSkyblock permissions: " + e.getMessage());
            return true; // Fallback - allow if error
        }
    }

    /**
     * Cache protection check result để tránh spam
     */
    private Boolean cachedProtectionResult = null;
    private long lastProtectionCheck = 0;
    private static final long PROTECTION_CACHE_TIME = 5000; // 5 seconds

    /**
     * Kiểm tra xem có thể farm tại location cụ thể không (với cache)
     */
    private boolean canFarmAtLocation(Location location) {
        long currentTime = System.currentTimeMillis();

        // Sử dụng cache result trong 5 giây để tránh spam check
        if (cachedProtectionResult != null && (currentTime - lastProtectionCheck) < PROTECTION_CACHE_TIME) {
            return cachedProtectionResult;
        }

        // Check protection và cache result
        boolean canFarm = canBreakBlockWorldGuard(location) && canBreakBlockSuperiorSkyblock(location);

        cachedProtectionResult = canFarm;
        lastProtectionCheck = currentTime;

        return canFarm;
    }

    /**
     * Kiểm tra WorldGuard cho block cụ thể (silent - no logging)
     */
    private boolean canBreakBlockWorldGuard(Location location) {
        try {
            if (Bukkit.getPluginManager().getPlugin("WorldGuard") == null) {
                return true;
            }

            RegionContainer container = WorldGuard.getInstance().getPlatform().getRegionContainer();
            RegionManager regions = container.get(BukkitAdapter.adapt(location.getWorld()));

            if (regions == null) {
                return true;
            }

            BlockVector3 blockVector = BukkitAdapter.asBlockVector(location);
            ApplicableRegionSet applicableRegions = regions.getApplicableRegions(blockVector);

            // Silent check - no logging để tránh spam
            com.sk89q.worldguard.protection.flags.StateFlag.State state = applicableRegions.queryState(null, Flags.BLOCK_BREAK);
            return (state != com.sk89q.worldguard.protection.flags.StateFlag.State.DENY);

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Kiểm tra Superior Skyblock cho block cụ thể (silent - no logging)
     */
    private boolean canBreakBlockSuperiorSkyblock(Location location) {
        try {
            if (Bukkit.getPluginManager().getPlugin("SuperiorSkyblock2") == null) {
                return true;
            }

            Island island = SuperiorSkyblockAPI.getGrid().getIslandAt(location);
            return island == null; // Allow if no island (wilderness)

        } catch (Exception e) {
            return true;
        }
    }

    /**
     * Bắt đầu farming task với proper logic
     */
    private void startFarmingTask(FarmingZoneInstance instance) {
        BukkitTask task = new BukkitRunnable() {
            private int ticks = 0;
            private final int maxTicks = instance.getDuration() * 20;
            private int cropTicks = 0;

            @Override
            public void run() {
                Player player = instance.getPlayer();
                FarmingZoneHologram hologram = instance.getHologram();

                if (ticks < maxTicks && player.isOnline() && hologram.isValid()) {
                    if (cropTicks >= TICKS_PER_CROP) {
                        farmNextCrop(hologram, instance.getFarmingRadius());
                        cropTicks = 0;
                    } else {
                        cropTicks++;
                    }
                    ticks++;
                } else {
                    FarmingZoneEffect.this.endFarmingZone(player.getUniqueId());
                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);

        instance.setTask(task);
    }

    /**
     * Farm next crop (proper logic)
     */
    private void farmNextCrop(FarmingZoneHologram hologram, double farmingRadius) {
        Location hologramLoc = hologram.getLocation();
        if (hologramLoc != null) {
            Player player = hologram.getOwner();
            List<Block> farmableCrops = findFarmableCrops(hologramLoc, farmingRadius);
            if (!farmableCrops.isEmpty()) {
                Block targetCrop = farmableCrops.get(new java.util.Random().nextInt(farmableCrops.size()));
                createLaserBeam(hologramLoc, targetCrop.getLocation().add(0.5, 0.5, 0.5));
                harvestAndReplant(player, targetCrop);
            }
        }
    }

    /**
     * Find farmable crops trong radius
     */
    private java.util.List<Block> findFarmableCrops(Location center, double farmingRadius) {
        java.util.List<Block> crops = new java.util.ArrayList<>();
        World world = center.getWorld();
        int centerX = center.getBlockX();
        int centerY = center.getBlockY();
        int centerZ = center.getBlockZ();

        for (int x = centerX - (int) farmingRadius; x <= centerX + farmingRadius; x++) {
            for (int y = centerY - 10; y <= centerY + 5; y++) {
                for (int z = centerZ - (int) farmingRadius; z <= centerZ + farmingRadius; z++) {
                    double distance = Math.sqrt(Math.pow(x - centerX, 2.0) + Math.pow(z - centerZ, 2.0));
                    if (distance <= farmingRadius) {
                        Block block = world.getBlockAt(x, y, z);
                        if (isMatureCrop(block) && canFarmAtLocation(block.getLocation())) {
                            crops.add(block);
                        }
                    }
                }
            }
        }
        return crops;
    }

    /**
     * Create laser beam effect
     */
    private void createLaserBeam(Location from, Location to) {
        double distance = from.distance(to);
        int particles = (int) (distance * 2.0);

        for (int i = 0; i <= particles; i++) {
            double ratio = (double) i / particles;
            double x = from.getX() + (to.getX() - from.getX()) * ratio;
            double y = from.getY() + (to.getY() - from.getY()) * ratio;
            double z = from.getZ() + (to.getZ() - from.getZ()) * ratio;
            Location particleLoc = new Location(from.getWorld(), x, y, z);
            from.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, particleLoc, 1, 0.0, 0.0, 0.0, 0.0);
        }
        to.getWorld().spawnParticle(Particle.HEART, to, 3, 0.2, 0.2, 0.2, 0.0);
    }

    /**
     * End farming zone
     */
    private void endFarmingZone(UUID playerId) {
        FarmingZoneInstance instance = activeFarmingZones.remove(playerId);
        if (instance != null) {
            if (instance.getTask() != null) {
                instance.getTask().cancel();
            }
            instance.getHologram().remove();
            Player player = instance.getPlayer();
            if (player != null && player.isOnline()) {
                player.sendMessage("§a[Farming Zone] §7Farming Zone đã kết thúc!");
            }
        }
    }

    /**
     * Kiểm tra xem block có phải mature crop không
     */
    private boolean isMatureCrop(Block block) {
        Material type = block.getType();
        
        if (!FARMABLE_CROPS.contains(type)) {
            return false;
        }

        if (block.getBlockData() instanceof Ageable) {
            Ageable ageable = (Ageable) block.getBlockData();
            return ageable.getAge() == ageable.getMaximumAge();
        }

        return false;
    }

    /**
     * Harvest và replant crop
     */
    private void harvestAndReplant(Player player, Block cropBlock) {
        Material cropType = cropBlock.getType();
        java.util.Collection<ItemStack> drops = cropBlock.getDrops();
        cropBlock.setType(Material.AIR);

        boolean anyAddedToStorage = false;
        int totalItemsHarvested = 0;

        for (ItemStack drop : drops) {
            totalItemsHarvested += drop.getAmount();
            boolean addedToStorage = tryAddToExtraStorage(player, drop);
            if (addedToStorage) {
                anyAddedToStorage = true;
                // Optional: Send message về storage
                // player.sendMessage("§7[Storage] §a+" + drop.getAmount() + " " + getItemDisplayName(drop));
            } else {
                player.getWorld().dropItemNaturally(player.getLocation(), drop);
            }
        }

        // Fire custom event
        try {
            // FarmingZoneEvent farmingEvent = new FarmingZoneEvent(player, cropBlock, drops, anyAddedToStorage, totalItemsHarvested, cropType.name());
            // Bukkit.getPluginManager().callEvent(farmingEvent);
        } catch (Exception e) {
            plugin.getLogger().warning("Error firing FarmingZoneEvent: " + e.getMessage());
        }

        // Replant crop
        Material replantType = CROP_TO_REPLANT_BLOCK.get(cropType);
        if (replantType != null) {
            Block soilBlock = cropBlock.getRelative(BlockFace.DOWN);
            if (soilBlock.getType() == Material.FARMLAND || soilBlock.getType() == Material.SOUL_SAND || soilBlock.getType() == Material.SOUL_SOIL) {
                try {
                    cropBlock.setType(replantType);
                    if (cropBlock.getBlockData() instanceof Ageable) {
                        Ageable ageable = (Ageable) cropBlock.getBlockData();
                        ageable.setAge(0);
                        cropBlock.setBlockData(ageable);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Error replanting crop: " + e.getMessage());
                }
            }
        }

        player.getWorld().playSound(cropBlock.getLocation(), Sound.BLOCK_CROP_BREAK, 0.5f, 1.0f);
    }

    /**
     * Try add to ExtraStorage với proper integration
     */
    private boolean tryAddToExtraStorage(Player player, ItemStack item) {
        try {
            if (Bukkit.getPluginManager().getPlugin("ExtraStorage") == null) {
                return false;
            }

            // Sử dụng reflection để access ExtraStorage API (proper classes)
            Class<?> storageAPIClass = Class.forName("me.hsgamer.extrastorage.api.StorageAPI");
            Object storageAPI = storageAPIClass.getMethod("getInstance").invoke(null);

            Class<?> userClass = Class.forName("me.hsgamer.extrastorage.api.user.User");
            Object user = storageAPIClass.getMethod("getUser", UUID.class).invoke(storageAPI, player.getUniqueId());

            if (user == null) {
                return false;
            }

            Object storage = userClass.getMethod("getStorage").invoke(user);
            if (storage == null) {
                return false;
            }

            Class<?> storageClass = Class.forName("me.hsgamer.extrastorage.api.storage.Storage");

            // Check storage status
            java.lang.reflect.Method getStatusMethod = storageClass.getMethod("getStatus");
            java.lang.reflect.Method isMaxSpaceMethod = storageClass.getMethod("isMaxSpace");

            boolean status = (Boolean) getStatusMethod.invoke(storage);
            boolean isMaxSpace = (Boolean) isMaxSpaceMethod.invoke(storage);

            if (status && !isMaxSpace) {
                // Convert ItemStack to material key
                String materialKey = item.getType().name();

                // Check if can store this material
                java.lang.reflect.Method canStoreMethod = storageClass.getMethod("canStore", Object.class);
                boolean canStore = (Boolean) canStoreMethod.invoke(storage, materialKey);

                // If can't store, try to add new item type
                if (!canStore) {
                    try {
                        java.lang.reflect.Method addNewItemMethod = storageClass.getMethod("addNewItem", Object.class);
                        addNewItemMethod.invoke(storage, materialKey);
                        canStore = (Boolean) canStoreMethod.invoke(storage, materialKey);
                        if (!canStore) {
                            return false;
                        }
                    } catch (Exception e) {
                        return false;
                    }
                }

                // Add item to storage using material key
                java.lang.reflect.Method addMethod = storageClass.getMethod("add", Object.class, int.class);
                addMethod.invoke(storage, materialKey, item.getAmount());
                return true;
            } else {
                return false;
            }

        } catch (Exception e) {
            plugin.getLogger().warning("Error adding to ExtraStorage: " + e.getMessage());
            return false;
        }
    }

    /**
     * Get item display name for messages
     */
    private String getItemDisplayName(ItemStack item) {
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        } else {
            String materialName = item.getType().name().toLowerCase().replace("_", " ");
            return materialName.substring(0, 1).toUpperCase() + materialName.substring(1);
        }
    }

    /**
     * Cleanup khi plugin disable
     */
    public void cleanup() {
        for (FarmingZoneInstance instance : activeFarmingZones.values()) {
            instance.deactivate();
        }
        activeFarmingZones.clear();
    }

    // Inner classes sẽ được thêm sau
    private static class FarmingZoneInstance {
        private final Player player;
        private final FarmingZoneHologram hologram;
        private final double radius;
        private final int duration;
        private BukkitTask task;
        private boolean active = true;

        public FarmingZoneInstance(Player player, FarmingZoneHologram hologram, double radius, int duration) {
            this.player = player;
            this.hologram = hologram;
            this.radius = radius;
            this.duration = duration;
        }

        public Player getPlayer() { return player; }
        public FarmingZoneHologram getHologram() { return hologram; }
        public double getRadius() { return radius; }
        public double getFarmingRadius() { return radius; }
        public int getDuration() { return duration; }
        public boolean isActive() { return active; }
        public BukkitTask getTask() { return task; }
        public void setTask(BukkitTask task) { this.task = task; }
        public void deactivate() {
            active = false;
            if (task != null) task.cancel();
            if (hologram != null) hologram.remove();
        }
    }

    private static class FarmingZoneHologram {
        private final Plugin plugin;
        private final Player player;
        private org.bukkit.entity.ArmorStand hologramEntity;

        public FarmingZoneHologram(Plugin plugin, Player player) {
            this.plugin = plugin;
            this.player = player;
            createHologram();
        }

        private void createHologram() {
            try {
                Location hologramLocation = player.getLocation().add(0, 2.5, 0);

                // Tạo ArmorStand làm hologram
                hologramEntity = player.getWorld().spawn(hologramLocation, org.bukkit.entity.ArmorStand.class);
                hologramEntity.setVisible(false);
                hologramEntity.setGravity(false);
                hologramEntity.setCanPickupItems(false);
                hologramEntity.setCustomNameVisible(true);
                hologramEntity.customName(net.kyori.adventure.text.Component.text("§a§l[FARMING ZONE]"));
                hologramEntity.setMarker(true);
                hologramEntity.setInvulnerable(true);

                plugin.getLogger().info("Created Farming Zone hologram for player: " + player.getName());

            } catch (Exception e) {
                plugin.getLogger().warning("Failed to create hologram: " + e.getMessage());
                // Fallback: sử dụng player head
                plugin.getLogger().info("Using player head as fallback for farming zone hologram");
            }
        }

        public Location getLocation() {
            if (hologramEntity != null && !hologramEntity.isDead()) {
                return hologramEntity.getLocation();
            }
            return player.getLocation().add(0, 2.5, 0);
        }

        public Player getOwner() {
            return player;
        }

        public boolean isValid() {
            return hologramEntity != null && !hologramEntity.isDead();
        }

        public void remove() {
            try {
                if (hologramEntity != null && !hologramEntity.isDead()) {
                    hologramEntity.remove();
                    plugin.getLogger().info("Removed Farming Zone hologram for player: " + player.getName());
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Error removing hologram: " + e.getMessage());
            }
        }
    }
}
