package shyrcs.dragon;

import org.bukkit.plugin.java.JavaPlugin;
import shyrcs.dragon.commands.DragonCommand;
import shyrcs.dragon.database.DragonDatabase;
import shyrcs.dragon.listeners.DragonListener;
import shyrcs.dragon.listeners.SetupItemListener;
import shyrcs.dragon.listeners.SummonListener;
import shyrcs.dragon.managers.DragonManager;
import shyrcs.dragon.managers.HologramManager;
import shyrcs.dragon.managers.SetupManager;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * SoulDragonCustom - Custom Dragon Battle System
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class SoulDragonPlugin extends JavaPlugin {

    private static SoulDragonPlugin instance;
    private static Logger console = Logger.getLogger("Minecraft");

    // Managers
    private DragonDatabase database;
    private SetupManager setupManager;
    private HologramManager hologramManager;
    private DragonManager dragonManager;
    private DragonListener dragonListener;

    @Override
    public void onEnable() {
        instance = this;

        try {
            // Save default config
            saveDefaultConfig();

            // Initialize database
            database = new DragonDatabase(getDataFolder());

            // Initialize managers
            setupManager = new SetupManager(this);
            hologramManager = new HologramManager(this);
            dragonManager = new DragonManager(this);
            dragonListener = new DragonListener(this);

            // Register commands
            getCommand("sdc").setExecutor(new DragonCommand(this));

            // Register listeners
            getServer().getPluginManager().registerEvents(new SetupItemListener(this), this);
            getServer().getPluginManager().registerEvents(new SummonListener(this), this);
            getServer().getPluginManager().registerEvents(dragonListener, this);

            info("SoulDragonCustom has been enabled successfully!");

        } catch (Exception e) {
            error("Failed to enable plugin: " + e.getMessage());
            e.printStackTrace();
            getServer().getPluginManager().disablePlugin(this);
        }
    }

    @Override
    public void onDisable() {
        try {
            // Cleanup dragon listener
            if (dragonListener != null) {
                dragonListener.cleanup();
            }

            // Cleanup holograms
            if (hologramManager != null) {
                hologramManager.cleanup();
            }

            // Cleanup dragons
            if (dragonManager != null) {
                dragonManager.cleanup();
            }

            // Close database
            if (database != null) {
                database.close();
            }

            info("SoulDragonCustom has been disabled successfully!");

        } catch (Exception e) {
            error("Error during plugin disable: " + e.getMessage());
        }
    }

    // Getters
    public static SoulDragonPlugin getInstance() {
        return instance;
    }

    public DragonDatabase getDatabase() {
        return database;
    }

    public SetupManager getSetupManager() {
        return setupManager;
    }

    public HologramManager getHologramManager() {
        return hologramManager;
    }

    public DragonManager getDragonManager() {
        return dragonManager;
    }

    // Logging utilities
    public static void info(String message) {
        console.log(Level.INFO, "[SoulDragonCustom] " + message);
    }

    public static void warn(String message) {
        console.log(Level.WARNING, "[SoulDragonCustom] " + message);
    }

    public static void error(String message) {
        console.log(Level.SEVERE, "[SoulDragonCustom] " + message);
    }
}