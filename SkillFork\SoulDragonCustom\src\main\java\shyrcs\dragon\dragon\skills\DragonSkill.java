package shyrcs.dragon.dragon.skills;

import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.CustomDragon;

/**
 * Base class for dragon skills
 */
public abstract class DragonSkill {
    
    protected final SoulDragonPlugin plugin;
    protected final CustomDragon customDragon;
    protected final String skillName;
    
    public DragonSkill(SoulDragonPlugin plugin, CustomDragon customDragon, String skillName) {
        this.plugin = plugin;
        this.customDragon = customDragon;
        this.skillName = skillName;
    }
    
    /**
     * Check if the skill can be used
     */
    public abstract boolean canUse();
    
    /**
     * Use the skill
     */
    public abstract void use();
    
    /**
     * Get skill cooldown in seconds
     */
    public abstract int getCooldownSeconds();
    
    /**
     * Get skill damage
     */
    public double getDamage() {
        return plugin.getConfig().getDouble("dragon.skills." + skillName + ".damage", 50.0);
    }
    
    /**
     * Get skill range
     */
    public double getRange() {
        return plugin.getConfig().getDouble("dragon.skills." + skillName + ".range", 10.0);
    }
    
    /**
     * Cleanup skill resources
     */
    public void cleanup() {
        // Override in subclasses if needed
    }
}
