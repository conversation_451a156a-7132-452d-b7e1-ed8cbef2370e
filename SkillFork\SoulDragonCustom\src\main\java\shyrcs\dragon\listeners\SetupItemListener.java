package shyrcs.dragon.listeners;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import shyrcs.dragon.SoulDragonPlugin;

/**
 * Listener for setup item interactions
 */
public class SetupItemListener implements Listener {
    
    private final SoulDragonPlugin plugin;
    
    public SetupItemListener(SoulDragonPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        Block clickedBlock = event.getClickedBlock();
        
        if (item == null || clickedBlock == null) {
            return;
        }
        
        // Check if player has permission
        if (!player.hasPermission("souldragon.setup")) {
            return;
        }
        
        // Check if it's a setup item
        if (!plugin.getSetupManager().isSetupItem(item)) {
            return;
        }
        
        String setupType = plugin.getSetupManager().getSetupType(item);
        if (setupType == null) {
            return;
        }
        
        // Cancel the event to prevent normal block interaction
        event.setCancelled(true);
        
        // Handle different setup types
        switch (setupType) {
            case "boss_position" -> handleBossPositionSetup(player, item, clickedBlock);
            case "altar" -> handleAltarSetup(player, item, clickedBlock);
            case "crystal" -> handleCrystalSetup(player, item, clickedBlock);
        }
    }
    
    /**
     * Handle boss position setup
     */
    private void handleBossPositionSetup(Player player, ItemStack item, Block block) {
        int required = plugin.getSetupManager().getRequiredCount("boss_position");
        
        if (item.getAmount() < required) {
            String message = plugin.getConfig().getString("messages.setup.insufficient_items", 
                "§c✗ Không đủ Que Quỷ Lửa!");
            player.sendMessage(message);
            return;
        }
        
        Location location = block.getLocation();
        
        // Save to database
        if (plugin.getDatabase().saveBossPosition(location)) {
            // Remove items
            if (item.getAmount() == required) {
                player.getInventory().setItemInMainHand(null);
            } else {
                item.setAmount(item.getAmount() - required);
            }
            
            String message = plugin.getConfig().getString("messages.setup.boss_position_set", 
                "§a✓ Đã đánh dấu vị trí triệu hồi boss!");
            player.sendMessage(message);
            
            SoulDragonPlugin.info("Boss position set at " + formatLocation(location) + " by " + player.getName());
        } else {
            player.sendMessage("§c✗ Lỗi khi lưu vị trí boss!");
        }
    }
    
    /**
     * Handle altar setup
     */
    private void handleAltarSetup(Player player, ItemStack item, Block block) {
        // Check if block is End Portal Frame
        if (block.getType() != Material.END_PORTAL_FRAME) {
            String message = plugin.getConfig().getString("messages.setup.invalid_block", 
                "§c✗ Block không hợp lệ!");
            player.sendMessage(message);
            return;
        }
        
        int required = plugin.getSetupManager().getRequiredCount("altar");
        
        if (item.getAmount() < required) {
            String message = plugin.getConfig().getString("messages.setup.insufficient_items", 
                "§c✗ Không đủ Que Quỷ Lửa!");
            player.sendMessage(message);
            return;
        }
        
        Location location = block.getLocation();
        
        // Save to database
        if (plugin.getDatabase().saveAltarPosition(location)) {
            // Remove items
            if (item.getAmount() == required) {
                player.getInventory().setItemInMainHand(null);
            } else {
                item.setAmount(item.getAmount() - required);
            }
            
            String message = plugin.getConfig().getString("messages.setup.altar_created", 
                "§a✓ Đã tạo tế đàn triệu hồi!");
            player.sendMessage(message);
            
            SoulDragonPlugin.info("Altar created at " + formatLocation(location) + " by " + player.getName());
        } else {
            player.sendMessage("§c✗ Lỗi khi lưu vị trí tế đàn!");
        }
    }
    
    /**
     * Handle crystal setup
     */
    private void handleCrystalSetup(Player player, ItemStack item, Block block) {
        int required = plugin.getSetupManager().getRequiredCount("crystal");
        
        if (item.getAmount() < required) {
            String message = plugin.getConfig().getString("messages.setup.insufficient_items", 
                "§c✗ Không đủ Que Quỷ Lửa!");
            player.sendMessage(message);
            return;
        }
        
        Location location = block.getLocation();
        
        // Save to database
        if (plugin.getDatabase().saveCrystalPosition(location)) {
            // Remove items
            if (item.getAmount() == required) {
                player.getInventory().setItemInMainHand(null);
            } else {
                item.setAmount(item.getAmount() - required);
            }
            
            String message = plugin.getConfig().getString("messages.setup.crystal_position_set", 
                "§a✓ Đã đánh dấu vị trí End Crystal!");
            player.sendMessage(message);
            
            SoulDragonPlugin.info("Crystal position set at " + formatLocation(location) + " by " + player.getName());
        } else {
            player.sendMessage("§c✗ Lỗi khi lưu vị trí crystal!");
        }
    }
    
    /**
     * Format location for logging
     */
    private String formatLocation(Location loc) {
        return String.format("%s: %.1f, %.1f, %.1f", 
            loc.getWorld().getName(), loc.getX(), loc.getY(), loc.getZ());
    }
}
