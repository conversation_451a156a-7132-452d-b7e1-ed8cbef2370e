package shyrcs.dragon.managers;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import shyrcs.dragon.SoulDragonPlugin;

import java.util.List;

/**
 * Manager for setup items and their creation
 */
public class SetupManager {
    
    private final SoulDragonPlugin plugin;
    
    public SetupManager(SoulDragonPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Create boss position setup item (1 Fire Rod)
     */
    public ItemStack createBossPositionItem() {
        ItemStack item = new ItemStack(Material.BLAZE_ROD, 1);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            String name = plugin.getConfig().getString("setup_items.boss_position.name", "§c§lQue Quỷ Lửa - Boss Position");
            List<String> lore = plugin.getConfig().getStringList("setup_items.boss_position.lore");
            
            meta.displayName(net.kyori.adventure.text.Component.text(name));
            
            if (!lore.isEmpty()) {
                meta.lore(lore.stream()
                    .map(line -> net.kyori.adventure.text.Component.text(line))
                    .toList());
            }
            
            // Add custom NBT to identify this as a setup item
            meta.getPersistentDataContainer().set(
                new org.bukkit.NamespacedKey(plugin, "setup_type"),
                org.bukkit.persistence.PersistentDataType.STRING,
                "boss_position"
            );
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Create altar setup item (8 Fire Rods)
     */
    public ItemStack createAltarItem() {
        ItemStack item = new ItemStack(Material.BLAZE_ROD, 8);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            String name = plugin.getConfig().getString("setup_items.altar.name", "§6§lQue Quỷ Lửa - Tế Đàn");
            List<String> lore = plugin.getConfig().getStringList("setup_items.altar.lore");
            
            meta.displayName(net.kyori.adventure.text.Component.text(name));
            
            if (!lore.isEmpty()) {
                meta.lore(lore.stream()
                    .map(line -> net.kyori.adventure.text.Component.text(line))
                    .toList());
            }
            
            // Add custom NBT to identify this as a setup item
            meta.getPersistentDataContainer().set(
                new org.bukkit.NamespacedKey(plugin, "setup_type"),
                org.bukkit.persistence.PersistentDataType.STRING,
                "altar"
            );
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Create crystal setup item (12 Fire Rods)
     */
    public ItemStack createCrystalItem() {
        ItemStack item = new ItemStack(Material.BLAZE_ROD, 12);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            String name = plugin.getConfig().getString("setup_items.crystal.name", "§d§lQue Quỷ Lửa - End Crystal");
            List<String> lore = plugin.getConfig().getStringList("setup_items.crystal.lore");
            
            meta.displayName(net.kyori.adventure.text.Component.text(name));
            
            if (!lore.isEmpty()) {
                meta.lore(lore.stream()
                    .map(line -> net.kyori.adventure.text.Component.text(line))
                    .toList());
            }
            
            // Add custom NBT to identify this as a setup item
            meta.getPersistentDataContainer().set(
                new org.bukkit.NamespacedKey(plugin, "setup_type"),
                org.bukkit.persistence.PersistentDataType.STRING,
                "crystal"
            );
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Create Ender Eye item for summoning
     */
    public ItemStack createEnderEyeItem() {
        ItemStack item = new ItemStack(Material.PLAYER_HEAD, 1);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.displayName(net.kyori.adventure.text.Component.text("§5§lMắt Ender"));
            meta.lore(List.of(
                net.kyori.adventure.text.Component.text("§7Chuột phải vào tế đàn để đặt"),
                net.kyori.adventure.text.Component.text("§7Cần 8 mắt để triệu hồi rồng")
            ));
            
            // Set custom texture using base64
            if (meta instanceof org.bukkit.inventory.meta.SkullMeta skullMeta) {
                try {
                    String texture = plugin.getConfig().getString("hologram.ender_eye.texture");
                    if (texture != null && !texture.isEmpty()) {
                        // Create PlayerProfile using Paper API
                        com.destroystokyo.paper.profile.PlayerProfile profile =
                            org.bukkit.Bukkit.createProfile(java.util.UUID.randomUUID(), "EnderEye");
                        
                        // Create texture property
                        com.destroystokyo.paper.profile.ProfileProperty property =
                            new com.destroystokyo.paper.profile.ProfileProperty("textures", texture);
                        
                        // Set texture property
                        profile.setProperty(property);
                        
                        // Use Paper API method to set profile directly
                        skullMeta.setPlayerProfile(profile);
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("Failed to set Ender Eye texture: " + e.getMessage());
                }
            }
            
            // Add custom NBT to identify this as an ender eye
            meta.getPersistentDataContainer().set(
                new org.bukkit.NamespacedKey(plugin, "item_type"),
                org.bukkit.persistence.PersistentDataType.STRING,
                "ender_eye"
            );
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Check if item is a setup item
     */
    public boolean isSetupItem(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(
            new org.bukkit.NamespacedKey(plugin, "setup_type"),
            org.bukkit.persistence.PersistentDataType.STRING
        );
    }
    
    /**
     * Get setup type from item
     */
    public String getSetupType(ItemStack item) {
        if (!isSetupItem(item)) {
            return null;
        }
        
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().get(
            new org.bukkit.NamespacedKey(plugin, "setup_type"),
            org.bukkit.persistence.PersistentDataType.STRING
        );
    }
    
    /**
     * Check if item is an ender eye
     */
    public boolean isEnderEye(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        String itemType = meta.getPersistentDataContainer().get(
            new org.bukkit.NamespacedKey(plugin, "item_type"),
            org.bukkit.persistence.PersistentDataType.STRING
        );
        
        return "ender_eye".equals(itemType);
    }
    
    /**
     * Get required item count for setup type
     */
    public int getRequiredCount(String setupType) {
        return switch (setupType) {
            case "boss_position" -> plugin.getConfig().getInt("setup_items.boss_position.cost", 1);
            case "altar" -> plugin.getConfig().getInt("setup_items.altar.cost", 8);
            case "crystal" -> plugin.getConfig().getInt("setup_items.crystal.cost", 12);
            default -> 1;
        };
    }
}
