package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.events.interaction.component.ButtonInteractionEvent;
import net.dv8tion.jda.api.hooks.ListenerAdapter;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.SbMagicHook;

import java.util.List;
import java.util.UUID;

/**
 * Listener để xử lý button interactions cho storage pagination
 */
public class ButtonListener extends ListenerAdapter {
    
    @Override
    public void onButtonInteraction(ButtonInteractionEvent event) {
        String buttonId = event.getComponentId();
        
        // Kiểm tra channel permissions
        if (!isChannelAllowed(event)) {
            event.reply(Library.config.getMessage("not-permitted"))
                .setEphemeral(true)
                .queue();
            return;
        }
        
        // Chỉ xử lý storage buttons
        if (!buttonId.startsWith("storage_")) {
            return;
        }
        
        try {
            // Parse button ID: storage_action_playerUuid_page
            String[] parts = buttonId.split("_");
            if (parts.length < 4) {
                event.reply("❌ Button ID không hợp lệ!").setEphemeral(true).queue();
                return;
            }
            
            String action = parts[1]; // prev, next, refresh
            String playerUuidStr = parts[2];
            int page = Integer.parseInt(parts[3]);
            
            UUID playerUuid;
            try {
                playerUuid = UUID.fromString(playerUuidStr);
            } catch (IllegalArgumentException e) {
                event.reply("❌ UUID không hợp lệ!").setEphemeral(true).queue();
                return;
            }
            
            // Kiểm tra quyền: chỉ owner mới được dùng buttons
            String discordId = event.getUser().getId();
            if (!isUserConnected(discordId)) {
                event.reply("❌ Bạn cần liên kết tài khoản trước!").setEphemeral(true).queue();
                return;
            }

            UUID userUuid = getMinecraftUUID(discordId);
            if (!playerUuid.equals(userUuid)) {
                event.reply("❌ Bạn chỉ có thể xem kho của chính mình!").setEphemeral(true).queue();
                return;
            }
            
            // Defer update để tránh timeout
            event.deferEdit().queue();
            
            // Xử lý action
            int newPage = page;
            switch (action) {
                case "prev":
                    newPage = Math.max(1, page - 1);
                    break;
                case "next":
                    newPage = page + 1;
                    break;
                case "refresh":
                    newPage = page;
                    break;
                default:
                    event.getHook().editOriginal("❌ Action không hợp lệ!").queue();
                    return;
            }
            
            // Tạo display mới
            StorageDisplayManager.StorageDisplay display = StorageDisplayManager.createStorageDisplay(playerUuid, newPage);
            
            // Update message
            if (display.getActionRows() != null && !display.getActionRows().isEmpty()) {
                event.getHook().editOriginalEmbeds(display.getEmbed())
                    .setComponents(display.getActionRows())
                    .queue();
            } else {
                event.getHook().editOriginalEmbeds(display.getEmbed())
                    .setComponents() // Clear components
                    .queue();
            }
            
        } catch (Exception e) {
            SbMagicHook.error("Error handling button interaction: " + e.getMessage());
            event.reply("❌ Có lỗi xảy ra khi xử lý button!").setEphemeral(true).queue();
        }
    }
    
    /**
     * Kiểm tra xem user đã kết nối chưa (kiểm tra cả database và memory)
     */
    private boolean isUserConnected(String discordId) {
        // Kiểm tra database trước
        if (Library.database.isConnected(discordId)) {
            return true;
        }

        // Fallback: kiểm tra in-memory storage
        return Library.storage.userConnected(discordId);
    }

    /**
     * Lấy Minecraft UUID (ưu tiên database, fallback memory)
     */
    private UUID getMinecraftUUID(String discordId) {
        // Thử database trước
        UUID uuid = Library.database.getMinecraftUUID(discordId);
        if (uuid != null) {
            return uuid;
        }

        // Fallback: thử in-memory storage
        return Library.storage.getMinecraftUUID(discordId);
    }

    /**
     * Kiểm tra xem channel có được phép sử dụng bot không
     */
    private boolean isChannelAllowed(ButtonInteractionEvent event) {
        List<String> whitelistedChannels = Library.config.getWhitelistedChannels();

        // Nếu không có whitelist thì cho phép tất cả
        if (whitelistedChannels == null || whitelistedChannels.isEmpty()) {
            return true;
        }

        String channelId = event.getChannel().getId();
        return whitelistedChannels.contains(channelId);
    }
}
