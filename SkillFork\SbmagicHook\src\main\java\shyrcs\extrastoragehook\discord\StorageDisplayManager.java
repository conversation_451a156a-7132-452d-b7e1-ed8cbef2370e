package shyrcs.extrastoragehook.discord;

import net.dv8tion.jda.api.EmbedBuilder;
import net.dv8tion.jda.api.entities.MessageEmbed;
import net.dv8tion.jda.api.interactions.components.ActionRow;
import net.dv8tion.jda.api.interactions.components.buttons.Button;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.SbMagicHook;

import java.awt.Color;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Manager để hiển thị storage với pagination và filtering
 */
public class StorageDisplayManager {
    
    private static final DecimalFormat formatter = new DecimalFormat("###,###");
    private static final Map<String, String> ITEM_EMOTES = new HashMap<>();
    
    static {
        // Load emotes từ config
        ITEM_EMOTES.put("DIAMOND", "💎");
        ITEM_EMOTES.put("EMERALD", "💚");
        ITEM_EMOTES.put("GOLD_INGOT", "🥇");
        ITEM_EMOTES.put("IRON_INGOT", "⚙️");
        ITEM_EMOTES.put("COAL", "⚫");
        ITEM_EMOTES.put("REDSTONE", "🔴");
        ITEM_EMOTES.put("LAPIS_LAZULI", "🔵");
        ITEM_EMOTES.put("QUARTZ", "⚪");
        ITEM_EMOTES.put("NETHERITE_INGOT", "🖤");
        ITEM_EMOTES.put("COPPER_INGOT", "🟠");
        ITEM_EMOTES.put("RAW_GOLD", "🟡");
        ITEM_EMOTES.put("RAW_IRON", "🟤");
        ITEM_EMOTES.put("RAW_COPPER", "🟠");
        ITEM_EMOTES.put("WHEAT", "🌾");
        ITEM_EMOTES.put("CARROT", "🥕");
        ITEM_EMOTES.put("POTATO", "🥔");
        ITEM_EMOTES.put("BEETROOT", "🍠");
    }
    
    /**
     * Tạo storage embed với pagination
     */
    public static StorageDisplay createStorageDisplay(UUID playerUuid, int page) {
        try {
            if (Library.extraStorageHook == null) {
                return new StorageDisplay(createErrorEmbed("❌ ExtraStorage chưa được kết nối!"), null);
            }
            
            // Lấy thông tin storage
            boolean storageStatus = Library.extraStorageHook.getStorageStatus(playerUuid);
            if (!storageStatus) {
                return new StorageDisplay(createErrorEmbed("❌ Kho ExtraStorage của bạn đang bị tắt!"), null);
            }
            
            long totalSpace = Library.extraStorageHook.getStorageSpace(playerUuid);
            long usedSpace = Library.extraStorageHook.getUsedSpace(playerUuid);
            
            // Lấy và filter items
            Map<String, Object> allItems = Library.extraStorageHook.getAllItems(playerUuid);
            List<String> itemFilter = Library.config.getItemFilter();
            
            Map<String, Long> filteredItems = new HashMap<>();
            if (allItems != null) {
                for (Map.Entry<String, Object> entry : allItems.entrySet()) {
                    String itemKey = entry.getKey();
                    
                    // Nếu có filter và item không trong filter thì skip
                    if (!itemFilter.isEmpty() && !itemFilter.contains(itemKey)) {
                        continue;
                    }
                    
                    try {
                        Object itemObj = entry.getValue();
                        // Lấy quantity từ item object (cần implement method trong ExtraStorageHook)
                        long quantity = Library.extraStorageHook.getItemAmount(playerUuid, itemKey);
                        if (quantity > 0) {
                            filteredItems.put(itemKey, quantity);
                        }
                    } catch (Exception e) {
                        SbMagicHook.error("Error getting item amount for " + itemKey + ": " + e.getMessage());
                    }
                }
            }
            
            if (filteredItems.isEmpty()) {
                return new StorageDisplay(createEmptyStorageEmbed(playerUuid, totalSpace, usedSpace), null);
            }
            
            // Pagination
            int itemsPerPage = Library.config.getItemsPerPage();
            List<Map.Entry<String, Long>> itemList = new ArrayList<>(filteredItems.entrySet());
            
            // Sort by quantity descending
            itemList.sort((a, b) -> Long.compare(b.getValue(), a.getValue()));
            
            int totalPages = (int) Math.ceil((double) itemList.size() / itemsPerPage);
            page = Math.max(1, Math.min(page, totalPages));
            
            int startIndex = (page - 1) * itemsPerPage;
            int endIndex = Math.min(startIndex + itemsPerPage, itemList.size());
            
            List<Map.Entry<String, Long>> pageItems = itemList.subList(startIndex, endIndex);
            
            // Tạo embed
            MessageEmbed embed = createStorageEmbed(playerUuid, totalSpace, usedSpace, pageItems, page, totalPages, itemList.size());
            
            // Tạo buttons
            List<ActionRow> actionRows = createPaginationButtons(page, totalPages, playerUuid.toString());
            
            return new StorageDisplay(embed, actionRows);
            
        } catch (Exception e) {
            SbMagicHook.error("Error creating storage display: " + e.getMessage());
            return new StorageDisplay(createErrorEmbed("❌ Có lỗi xảy ra khi lấy thông tin kho!"), null);
        }
    }
    
    /**
     * Tạo embed cho storage
     */
    private static MessageEmbed createStorageEmbed(UUID playerUuid, long totalSpace, long usedSpace, 
                                                  List<Map.Entry<String, Long>> items, int page, int totalPages, int totalItems) {
        EmbedBuilder embed = new EmbedBuilder();
        embed.setTitle("📦 Thông tin kho ExtraStorage");
        embed.setColor(Color.BLUE);
        
        // Thông tin dung lượng
        String spaceInfo;
        if (totalSpace == -1) {
            spaceInfo = "♾️ Không giới hạn";
        } else {
            long freeSpace = totalSpace - usedSpace;
            spaceInfo = String.format("%s / %s (%s còn trống)", 
                formatter.format(usedSpace), 
                formatter.format(totalSpace), 
                formatter.format(freeSpace));
        }
        
        embed.addField("💾 Dung lượng", spaceInfo, true);
        embed.addField("📊 Trạng thái", "✅ Hoạt động", true);
        embed.addField("📄 Trang", String.format("%d/%d", page, totalPages), true);
        
        // Danh sách items
        StringBuilder itemList = new StringBuilder();
        for (Map.Entry<String, Long> entry : items) {
            String itemKey = entry.getKey();
            long quantity = entry.getValue();
            
            String emote = ITEM_EMOTES.getOrDefault(itemKey, "📦");
            String itemName = formatItemName(itemKey);
            
            itemList.append(String.format("%s **%s**: %s\n", 
                emote, itemName, formatter.format(quantity)));
        }
        
        // Thêm thông tin items khác nếu có
        int remainingItems = totalItems - (page * Library.config.getItemsPerPage());
        if (remainingItems > 0) {
            itemList.append(String.format("\n*...và %d items khác*", remainingItems));
        }
        
        embed.addField("📋 Items trong kho", itemList.toString(), false);
        
        embed.setFooter("💡 Sử dụng /sell <item> để bán items hoặc /sell all để bán tất cả");
        
        return embed.build();
    }
    
    /**
     * Tạo embed khi kho trống
     */
    private static MessageEmbed createEmptyStorageEmbed(UUID playerUuid, long totalSpace, long usedSpace) {
        EmbedBuilder embed = new EmbedBuilder();
        embed.setTitle("📦 Thông tin kho ExtraStorage");
        embed.setColor(Color.GRAY);
        
        String spaceInfo;
        if (totalSpace == -1) {
            spaceInfo = "♾️ Không giới hạn";
        } else {
            spaceInfo = String.format("%s / %s", 
                formatter.format(usedSpace), 
                formatter.format(totalSpace));
        }
        
        embed.addField("💾 Dung lượng", spaceInfo, true);
        embed.addField("📊 Trạng thái", "✅ Hoạt động", true);
        embed.addField("📋 Items", "📦 Kho của bạn đang trống!", false);
        
        return embed.build();
    }
    
    /**
     * Tạo error embed
     */
    private static MessageEmbed createErrorEmbed(String message) {
        EmbedBuilder embed = new EmbedBuilder();
        embed.setTitle("❌ Lỗi");
        embed.setDescription(message);
        embed.setColor(Color.RED);
        return embed.build();
    }
    
    /**
     * Tạo pagination buttons
     */
    private static List<ActionRow> createPaginationButtons(int currentPage, int totalPages, String playerUuid) {
        List<Button> buttons = new ArrayList<>();
        
        // Previous button
        Button prevButton = Button.secondary("storage_prev_" + playerUuid + "_" + (currentPage - 1), "◀️ Trang trước")
            .withDisabled(currentPage <= 1);
        
        // Refresh button
        Button refreshButton = Button.primary("storage_refresh_" + playerUuid + "_" + currentPage, "🔄 Làm mới");
        
        // Next button  
        Button nextButton = Button.secondary("storage_next_" + playerUuid + "_" + (currentPage + 1), "Trang sau ▶️")
            .withDisabled(currentPage >= totalPages);
        
        buttons.add(prevButton);
        buttons.add(refreshButton);
        buttons.add(nextButton);
        
        return Collections.singletonList(ActionRow.of(buttons));
    }
    
    /**
     * Format item name để hiển thị đẹp hơn
     */
    private static String formatItemName(String itemKey) {
        return Arrays.stream(itemKey.toLowerCase().split("_"))
            .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1))
            .collect(Collectors.joining(" "));
    }
    
    /**
     * Class để chứa kết quả display
     */
    public static class StorageDisplay {
        private final MessageEmbed embed;
        private final List<ActionRow> actionRows;
        
        public StorageDisplay(MessageEmbed embed, List<ActionRow> actionRows) {
            this.embed = embed;
            this.actionRows = actionRows;
        }
        
        public MessageEmbed getEmbed() {
            return embed;
        }
        
        public List<ActionRow> getActionRows() {
            return actionRows;
        }
    }
}
