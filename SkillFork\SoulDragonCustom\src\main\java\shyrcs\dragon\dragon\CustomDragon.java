package shyrcs.dragon.dragon;

import org.bukkit.Location;
import org.bukkit.entity.EnderDragon;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.skills.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * Custom dragon with AI and skills
 */
public class CustomDragon {
    
    private final SoulDragonPlugin plugin;
    private final EnderDragon dragon;
    private final Location homeLocation;
    private final Random random;
    
    // Skills
    private final Map<String, DragonSkill> skills;
    private final Map<String, Long> skillCooldowns;
    
    // AI Task
    private BukkitTask aiTask;
    private int aiTicks;
    
    // AI States
    private DragonAIState currentState;
    private Player currentTarget;
    private long lastSkillUse;
    
    public CustomDragon(SoulDragonPlugin plugin, EnderDragon dragon) {
        this.plugin = plugin;
        this.dragon = dragon;
        this.homeLocation = dragon.getLocation().clone();
        this.random = new Random();
        
        this.skills = new HashMap<>();
        this.skillCooldowns = new HashMap<>();
        this.aiTicks = 0;
        this.currentState = DragonAIState.IDLE;
        this.lastSkillUse = 0;
        
        initializeSkills();
    }
    
    /**
     * Initialize dragon skills
     */
    private void initializeSkills() {
        skills.put("rush", new RushSkill(plugin, this));
        skills.put("breath", new DragonBreathSkill(plugin, this));
        skills.put("lightning", new LightningStrikeSkill(plugin, this));
        skills.put("crystal_heal", new CrystalHealSkill(plugin, this));
        skills.put("roar", new RoarSkill(plugin, this));
        skills.put("shadow_clone", new ShadowCloneSkill(plugin, this));
    }
    
    /**
     * Start AI behavior
     */
    public void startAI() {
        if (aiTask != null && !aiTask.isCancelled()) {
            aiTask.cancel();
        }
        
        aiTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (dragon.isDead() || !dragon.isValid()) {
                    this.cancel();
                    return;
                }
                
                updateAI();
                aiTicks++;
            }
        }.runTaskTimer(plugin, 0L, 1L); // Run every tick
    }
    
    /**
     * Update AI behavior
     */
    private void updateAI() {
        // Keep dragon in designated area
        enforceAreaRestriction();
        
        // Find target
        updateTarget();
        
        // Execute AI based on current state
        switch (currentState) {
            case IDLE -> handleIdleState();
            case COMBAT -> handleCombatState();
            case SKILL_CASTING -> handleSkillCastingState();
        }
        
        // Use skills periodically
        if (aiTicks % 60 == 0) { // Every 3 seconds
            considerUsingSkill();
        }
    }
    
    /**
     * Keep dragon within designated area
     */
    private void enforceAreaRestriction() {
        double maxDistance = 100.0; // Maximum distance from home
        
        if (dragon.getLocation().distance(homeLocation) > maxDistance) {
            // Teleport dragon back towards home
            Location targetLocation = homeLocation.clone().add(
                random.nextDouble() * 20 - 10,
                random.nextDouble() * 10,
                random.nextDouble() * 20 - 10
            );
            
            dragon.teleport(targetLocation);
        }
    }
    
    /**
     * Update current target
     */
    private void updateTarget() {
        Player nearestPlayer = null;
        double nearestDistance = Double.MAX_VALUE;
        
        for (Player player : dragon.getWorld().getPlayers()) {
            if (player.getGameMode() == org.bukkit.GameMode.CREATIVE || 
                player.getGameMode() == org.bukkit.GameMode.SPECTATOR) {
                continue;
            }
            
            double distance = player.getLocation().distance(dragon.getLocation());
            if (distance < 50.0 && distance < nearestDistance) {
                nearestDistance = distance;
                nearestPlayer = player;
            }
        }
        
        currentTarget = nearestPlayer;
        
        // Update AI state based on target
        if (currentTarget != null) {
            if (currentState == DragonAIState.IDLE) {
                currentState = DragonAIState.COMBAT;
            }
        } else {
            if (currentState == DragonAIState.COMBAT) {
                currentState = DragonAIState.IDLE;
            }
        }
    }
    
    /**
     * Handle idle state
     */
    private void handleIdleState() {
        // Fly around home location
        if (aiTicks % 100 == 0) { // Every 5 seconds
            Location randomLocation = homeLocation.clone().add(
                random.nextDouble() * 40 - 20,
                random.nextDouble() * 20 + 10,
                random.nextDouble() * 40 - 20
            );
            
            // Set dragon's target location (this is a simplified approach)
            // In a real implementation, you might want to use NMS or custom pathfinding
        }
    }
    
    /**
     * Handle combat state
     */
    private void handleCombatState() {
        if (currentTarget == null) {
            return;
        }
        
        // Move towards target
        Location targetLocation = currentTarget.getLocation().clone().add(0, 15, 0);
        double distance = dragon.getLocation().distance(targetLocation);
        
        // Adjust behavior based on distance
        if (distance > 30) {
            // Move closer
        } else if (distance < 10) {
            // Move away
        }
    }
    
    /**
     * Handle skill casting state
     */
    private void handleSkillCastingState() {
        // This state is managed by individual skills
        // Return to combat state after a short time
        if (System.currentTimeMillis() - lastSkillUse > 3000) {
            currentState = DragonAIState.COMBAT;
        }
    }
    
    /**
     * Consider using a skill
     */
    private void considerUsingSkill() {
        if (currentTarget == null || currentState == DragonAIState.SKILL_CASTING) {
            return;
        }
        
        // Check health percentage for different skill priorities
        double healthPercentage = dragon.getHealth() / dragon.getMaxHealth();
        
        String skillToUse = null;
        
        if (healthPercentage < 0.3) {
            // Low health - prioritize healing and defensive skills
            if (isSkillReady("crystal_heal")) {
                skillToUse = "crystal_heal";
            } else if (isSkillReady("roar")) {
                skillToUse = "roar";
            }
        } else if (healthPercentage < 0.6) {
            // Medium health - use all skills
            String[] allSkills = {"rush", "breath", "lightning", "roar", "shadow_clone"};
            for (String skill : allSkills) {
                if (isSkillReady(skill) && random.nextDouble() < 0.3) {
                    skillToUse = skill;
                    break;
                }
            }
        } else {
            // High health - use offensive skills
            String[] offensiveSkills = {"rush", "breath", "lightning"};
            for (String skill : offensiveSkills) {
                if (isSkillReady(skill) && random.nextDouble() < 0.2) {
                    skillToUse = skill;
                    break;
                }
            }
        }
        
        if (skillToUse != null) {
            useSkill(skillToUse);
        }
    }
    
    /**
     * Check if skill is ready (not on cooldown)
     */
    private boolean isSkillReady(String skillName) {
        Long lastUse = skillCooldowns.get(skillName);
        if (lastUse == null) {
            return true;
        }
        
        DragonSkill skill = skills.get(skillName);
        if (skill == null) {
            return false;
        }
        
        long cooldownMs = skill.getCooldownSeconds() * 1000L;
        return System.currentTimeMillis() - lastUse >= cooldownMs;
    }
    
    /**
     * Use a skill
     */
    public void useSkill(String skillName) {
        DragonSkill skill = skills.get(skillName);
        if (skill == null || !isSkillReady(skillName)) {
            return;
        }
        
        if (skill.canUse()) {
            skill.use();
            skillCooldowns.put(skillName, System.currentTimeMillis());
            lastSkillUse = System.currentTimeMillis();
            currentState = DragonAIState.SKILL_CASTING;
            
            SoulDragonPlugin.info("Dragon used skill: " + skillName);
        }
    }
    
    /**
     * Get the dragon entity
     */
    public EnderDragon getDragon() {
        return dragon;
    }
    
    /**
     * Get current target
     */
    public Player getCurrentTarget() {
        return currentTarget;
    }
    
    /**
     * Get home location
     */
    public Location getHomeLocation() {
        return homeLocation.clone();
    }
    
    /**
     * Cleanup dragon AI and skills
     */
    public void cleanup() {
        if (aiTask != null && !aiTask.isCancelled()) {
            aiTask.cancel();
        }
        
        for (DragonSkill skill : skills.values()) {
            skill.cleanup();
        }
    }
    
    /**
     * Dragon AI states
     */
    public enum DragonAIState {
        IDLE,
        COMBAT,
        SKILL_CASTING
    }
}
