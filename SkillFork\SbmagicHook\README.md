# SbMagicHook - v1.0.0-BETA

Plugin kết nối ExtraStorage với Discord, cho phép người chơi quản lý kho và bán items thông qua Discord bot.

## Tính năng

- 🔗 **Kết nối tài khoản**: <PERSON>ên kết tài khoản Discord với Minecraft
- 📦 **Quản lý kho**: Xem thông tin kho ExtraStorage qua Discord
- 💰 **Bán items**: Bán items từ kho trực tiếp qua Discord
- 🛒 **Tích hợp Shop**: Hỗ trợ ShopGUI+ và EconomyShopGUI
- 💾 **Lưu trữ bền vững**: Kết nối được lưu và không mất khi restart
- ⚡ **Hiệu suất cao**: Xử lý bất đồng bộ, không lag server

## Dependencies

### Bắt buộc
- **ExtraStorage**: Plugin kho chính
- **Vault**: Hệ thống economy
- **Discord Bot**: <PERSON>ần tạo bot trên Discord Developer Portal

### Tùy chọn
- **ShopGUI+**: Để sử dụng giá từ shop
- **EconomyShopGUI**: Alternative shop plugin
- **PlaceholderAPI**: Để sử dụng placeholders

## Cài đặt

1. **Tải plugin** và đặt vào thư mục `plugins/`
2. **Tạo Discord Bot**:
   - Truy cập [Discord Developer Portal](https://discord.com/developers/applications)
   - Tạo New Application → Bot
   - Copy Bot Token
   - Invite bot vào server với quyền:
     - Send Messages
     - Use Slash Commands
     - Read Message History
     - Embed Links

3. **Cấu hình plugin**:
   - Khởi động server để tạo file config
   - Chỉnh sửa `plugins/SbMagicHook/config.yml`
   - Điền Bot Token và Guild ID
   - Restart server

## Cấu hình

### Discord Settings
```yaml
discord:
  bot-token: "YOUR_BOT_TOKEN_HERE"
  guild-id: "YOUR_GUILD_ID_HERE"
  use-slash-commands: true
  whitelisted-channels: [] # Để trống = tất cả channels
```

### Economy Settings
```yaml
economy:
  use-price-provider: true
  provider: "ShopGUIPlus" # ShopGUIPlus, EconomyShopGUI, Vault
  money-format: "###,###.##"
```

### ExtraStorage Integration
```yaml
extrastorage:
  auto-sell-when-full: false
  allow-sell-all: true
  max-sell-amount: 999999
```

## Sử dụng

### Kết nối tài khoản
1. **Discord**: `/connect` hoặc `!connect`
2. **Minecraft**: `/sbmagichook hook <mã>`

### Commands Discord
- `/connect` - Tạo mã kết nối để liên kết tài khoản Discord với Minecraft
- `/link` - Alias cho lệnh connect
- `/unlink` - Hủy liên kết tài khoản Discord với Minecraft
- `/storage` - Xem thông tin kho ExtraStorage
- `/sell <item> [amount]` - Bán items từ kho
- `/stats` - Xem thống kê tài khoản của bạn
- `/help` - Danh sách lệnh

### Commands Minecraft
- `/sbmagichook hook <code>` - Kết nối tài khoản
- `/sbmagichook reload` - Tải lại config
- `/sbmagichook info` - Thông tin plugin

## Ví dụ

### Bán items
```
/sell diamond 64        # Bán 64 diamond
/sell iron_ingot all    # Bán tất cả iron ingot
!sell gold_ingot 128    # Chat command
```

### Xem kho
```
/storage                # Xem thông tin kho
!storage                # Chat command
```

## Permissions

- `sbmagichook.use.hook` - Kết nối tài khoản (default: true)
- `sbmagichook.use.sell` - Sử dụng lệnh sell (default: true)
- `sbmagichook.admin` - Admin permissions (default: op)
- `sbmagichook.reload` - Reload plugin (default: op)

## API cho Developers

### Lấy thông tin kho
```java
ExtraStorageHook hook = Library.extraStorageHook;
Storage storage = hook.getStorage(playerUUID);
long amount = storage.getQuantity("DIAMOND");
```

### Kiểm tra kết nối
```java
StorageHook storageHook = Library.storage;
boolean connected = storageHook.userConnected(discordId);
UUID uuid = storageHook.getMinecraftUUID(discordId);
```

## Troubleshooting

### Bot không hoạt động
- Kiểm tra Bot Token có đúng không
- Kiểm tra bot có được invite vào server không
- Kiểm tra Guild ID có đúng không

### Lỗi DISALLOWED_INTENTS (CloseCode 4014)
Nếu gặp lỗi này trong console:
```
CloseCode[DISALLOWED_INTENTS](code=4014, meaning=Disallowed intents...)
```

**Cách khắc phục:**
1. Vào [Discord Developer Portal](https://discord.com/developers/applications)
2. Chọn bot application của bạn
3. Vào tab **"Bot"**
4. Trong phần **"Privileged Gateway Intents"**, bật:
   - ✅ **MESSAGE CONTENT INTENT** (bắt buộc)
   - ⚠️ **SERVER MEMBERS INTENT** (tùy chọn)
   - ⚠️ **PRESENCE INTENT** (tùy chọn)

**Lưu ý:** Plugin hiện tại chỉ cần MESSAGE_CONTENT INTENT để hoạt động.

### Slash Commands timeout ("Bot didn't respond in time")
Nếu slash commands bị timeout nhưng message commands hoạt động:

**Nguyên nhân:** Plugin đã được cập nhật để tuân thủ JDA v5 và Discord API requirements.

**Giải pháp:**
- ✅ **Đã sửa** trong phiên bản mới - slash commands giờ sử dụng `deferReply()` để tránh timeout
- ✅ **Auto-register** slash commands khi bot khởi động
- ✅ **Xóa commands cũ** và đăng ký lại để tránh conflict

**Kết quả mong đợi:**
```
[INFO]: ✅ Đã đăng ký 4 slash commands thành công!
[INFO]:   - /connect: Tạo mã kết nối để liên kết tài khoản Discord với Minecraft
[INFO]:   - /sell: Bán items từ kho ExtraStorage
[INFO]:   - /storage: Xem thông tin kho ExtraStorage
[INFO]:   - /help: Hiển thị danh sách lệnh
```

### Không bán được items
- Kiểm tra ExtraStorage có hoạt động không
- Kiểm tra economy provider (ShopGUI+/Vault)
- Kiểm tra item có trong kho không

### Kết nối bị mất
- Plugin tự động lưu kết nối vào file
- Kiểm tra file `connections.yml` trong thư mục plugin

## Changelog

### v1.0.0-BETA
- Phiên bản đầu tiên
- Kết nối ExtraStorage với Discord
- Hỗ trợ bán items qua Discord
- Tích hợp ShopGUI+ và Vault
- Lưu trữ kết nối bền vững

## Support

- **GitHub**: [Repository Link]
- **Discord**: [Support Server]
- **Issues**: Báo lỗi qua GitHub Issues

## License

MIT License - Xem file LICENSE để biết thêm chi tiết.

---

**Lưu ý**: Plugin này là phiên bản BETA, có thể có bugs. Vui lòng backup dữ liệu trước khi sử dụng.
