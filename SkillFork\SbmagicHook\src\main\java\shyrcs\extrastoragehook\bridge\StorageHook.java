package shyrcs.extrastoragehook.bridge;



import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Storage Hook để quản lý kết nối giữa Discord ID và Minecraft UUID
 * In-memory cache cho connections (persistent storage qua LiteDatabase)
 */
public class StorageHook {

    private final Map<String, UUID> connections;

    public StorageHook() {
        this.connections = Collections.synchronizedMap(new HashMap<>());
    }
    
    /**
     * Kết nối Discord ID với Minecraft UUID (chỉ in-memory)
     */
    public synchronized void connect(String discordId, UUID minecraftUuid) {
        this.connections.put(discordId, minecraftUuid);
    }

    /**
     * Ngắt kết nối Discord ID (chỉ in-memory)
     */
    public synchronized void disconnect(String discordId) {
        this.connections.remove(discordId);
    }
    
    /**
     * Lấy Minecraft UUID từ Discord ID
     */
    public synchronized UUID getMinecraftUUID(String discordId) {
        return this.connections.get(discordId);
    }
    
    /**
     * L<PERSON>y Discord ID từ Minecraft UUID
     */
    public synchronized String getDiscordID(UUID minecraftUuid) {
        for (Map.Entry<String, UUID> entry : connections.entrySet()) {
            if (entry.getValue().equals(minecraftUuid)) {
                return entry.getKey();
            }
        }
        return null;
    }
    
    /**
     * Kiểm tra Discord user đã kết nối chưa
     */
    public synchronized boolean userConnected(String discordId) {
        return this.connections.containsKey(discordId);
    }
    
    /**
     * Kiểm tra Minecraft player đã kết nối chưa
     */
    public synchronized boolean playerConnected(UUID minecraftUuid) {
        return this.connections.containsValue(minecraftUuid);
    }
    
    /**
     * Lấy số lượng kết nối hiện tại
     */
    public synchronized int getConnectionCount() {
        return this.connections.size();
    }
    
    /**
     * Xóa tất cả kết nối
     */
    public synchronized void clearAll() {
        this.connections.clear();
    }
    
    /**
     * Lấy tất cả kết nối (chỉ để debug)
     */
    public synchronized Map<String, UUID> getAllConnections() {
        return new HashMap<>(this.connections);
    }
}
