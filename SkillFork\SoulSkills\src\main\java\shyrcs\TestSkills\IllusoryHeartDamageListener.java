package shyrcs.TestSkills;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.metadata.MetadataValue;
import org.bukkit.plugin.Plugin;

/**
 * Listener để xử lý damage boost từ Illusory Heart
 */
public class IllusoryHeartDamageListener implements Listener {
    
    private final Plugin plugin;
    
    public IllusoryHeartDamageListener(Plugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler(priority = EventPriority.NORMAL)
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        // Kiểm tra xem người gây damage có phải là player không
        if (!(event.getDamager() instanceof Player)) {
            return;
        }
        
        Player player = (Player) event.getDamager();
        
        // Kiểm tra xem player có damage boost không
        if (!player.hasMetadata("illusory_heart_damage_boost")) {
            return;
        }
        
        // Kiểm tra xem boost có hết hạn chưa
        if (player.hasMetadata("illusory_heart_damage_boost_expire")) {
            long expireTime = getMetadataValue(player, "illusory_heart_damage_boost_expire");
            if (System.currentTimeMillis() > expireTime) {
                // Đã hết hạn, xóa metadata
                player.removeMetadata("illusory_heart_damage_boost", plugin);
                player.removeMetadata("illusory_heart_damage_boost_expire", plugin);
                return;
            }
        }
        
        // Lấy giá trị damage boost
        double damageBoost = getMetadataValue(player, "illusory_heart_damage_boost");
        
        // Tính toán damage mới
        double originalDamage = event.getDamage();
        double newDamage = originalDamage * (1 + damageBoost / 100.0);
        
        // Áp dụng damage mới
        event.setDamage(newDamage);

        // Hiệu ứng khi gây damage
        player.getWorld().spawnParticle(org.bukkit.Particle.FLAME,
            event.getEntity().getLocation().add(0, 1, 0), 5, 0.3, 0.3, 0.3, 0.05);
    }
    
    /**
     * Helper method để lấy giá trị metadata
     */
    @SuppressWarnings("unchecked")
    private <T> T getMetadataValue(Player player, String key) {
        for (MetadataValue value : player.getMetadata(key)) {
            if (value.getOwningPlugin().equals(plugin)) {
                return (T) value.value();
            }
        }
        return null;
    }
}
