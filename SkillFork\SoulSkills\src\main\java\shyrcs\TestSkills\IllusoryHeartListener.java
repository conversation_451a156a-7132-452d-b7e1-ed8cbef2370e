package shyrcs.TestSkills;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.block.Action;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;
import io.lumine.mythic.lib.api.item.NBTItem;
import shyrcs.Ability.CooldownManager;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Listener để xử lý Illusory Heart skill activation
 */
public class IllusoryHeartListener implements Listener {
    
    private final Plugin plugin;
    private final IllusoryHeartEffect illusoryHeartEffect;
    private final CooldownManager cooldownManager;
    
    public IllusoryHeartListener(Plugin plugin) {
        this.plugin = plugin;
        this.illusoryHeartEffect = new IllusoryHeartEffect(plugin);
        this.cooldownManager = CooldownManager.getInstance(plugin);
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        if (item == null) return;
        
        // Kiểm tra NBT cho Illusory Heart skill trước
        IllusoryHeartConfig config = parseIllusoryHeartNBT(item);
        if (config == null) return;

        // Kiểm tra action dựa trên config
        Action action = event.getAction();
        boolean isValidClick = false;

        String clickType = config.getClickType().toLowerCase();
        switch (clickType) {
            case "right_click":
                isValidClick = (action == Action.RIGHT_CLICK_AIR || action == Action.RIGHT_CLICK_BLOCK);
                break;
            case "left_click":
                isValidClick = (action == Action.LEFT_CLICK_AIR || action == Action.LEFT_CLICK_BLOCK);
                break;
            case "shift_right_click":
                isValidClick = (action == Action.RIGHT_CLICK_AIR || action == Action.RIGHT_CLICK_BLOCK) && player.isSneaking();
                break;
            case "shift_left_click":
                isValidClick = (action == Action.LEFT_CLICK_AIR || action == Action.LEFT_CLICK_BLOCK) && player.isSneaking();
                break;
            default:
                // Default: right click hoặc shift + left click
                isValidClick = (action == Action.RIGHT_CLICK_AIR || action == Action.RIGHT_CLICK_BLOCK) ||
                              ((action == Action.LEFT_CLICK_AIR || action == Action.LEFT_CLICK_BLOCK) && player.isSneaking());
                break;
        }

        if (!isValidClick) return;
        
        // Kiểm tra cooldown
        String skillType = "illusory_heart";
        if (cooldownManager.isOnCooldown(player, skillType)) {
            long remainingTime = cooldownManager.getRemainingCooldown(player, skillType);
            player.sendMessage("§c[Illusory Heart] §7Còn lại §c" + (remainingTime / 1000) + "s §7cooldown!");
            return;
        }
        
        // Kích hoạt skill
        event.setCancelled(true);
        
        illusoryHeartEffect.createShrineOfMaya(
            player,
            config.getDamageBoost(),
            config.getLightningDamage(),
            config.getHealPerSecond(),
            config.getSpeedBoost(),
            config.getCooldown()
        );
        
        // Đặt cooldown
        cooldownManager.setCooldown(player, skillType, config.getCooldown());
    }
    
    /**
     * Tìm illusory_heart config trong NBT tags của item
     * Format mới: soulskills = "illusory_heart <damageBoost%> <lightningDamage> <healPerSecond> <speedBoost%> <click> <cooldown>"
     */
    private String findIllusoryHeartConfig(NBTItem nbtItem) {
        // Kiểm tra tag "soulskills" trước (format mới)
        if (nbtItem.hasTag("soulskills")) {
            String value = nbtItem.getString("soulskills");
            if (value != null && value.startsWith("illusory_heart")) {
                return value;
            }
        }

        // Fallback: kiểm tra các tag cũ
        String[] possibleTags = {
            "MMOITEMS_ILLUSORY_HEART",
            "illusory_heart",
            "ILLUSORY_HEART"
        };

        for (String tag : possibleTags) {
            if (nbtItem.hasTag(tag)) {
                String value = nbtItem.getString(tag);
                if (value != null && !value.trim().isEmpty()) {
                    return value;
                }
            }
        }

        return null;
    }

    /**
     * Parse NBT để lấy thông tin Illusory Heart skill
     * Format: illusory_heart <damageBoost%> <lightningDamage> <healPerSecond> <speedBoost%> <click> <cooldown>
     */
    private IllusoryHeartConfig parseIllusoryHeartNBT(ItemStack item) {
        try {
            NBTItem nbtItem = NBTItem.get(item);

            String skillData = findIllusoryHeartConfig(nbtItem);
            if (skillData == null || skillData.trim().isEmpty()) {
                return null;
            }

            // Parse skill data
            String[] parts = skillData.trim().split("\\s+");
            if (parts.length < 7) {
                plugin.getLogger().warning("Invalid Illusory Heart NBT format: " + skillData + " (expected 7 parts)");
                return null;
            }

            // Bỏ qua phần đầu "illusory_heart"
            if (parts[0].equalsIgnoreCase("illusory_heart")) {
                double damageBoost = Double.parseDouble(parts[1]);
                double lightningDamage = Double.parseDouble(parts[2]);
                double healPerSecond = Double.parseDouble(parts[3]);
                double speedBoost = Double.parseDouble(parts[4]);
                String clickType = parts[5]; // right_click, left_click, etc.
                int cooldown = Integer.parseInt(parts[6]);

                return new IllusoryHeartConfig(damageBoost, lightningDamage, healPerSecond, speedBoost, clickType, cooldown);
            }

        } catch (Exception e) {
            plugin.getLogger().warning("Error parsing Illusory Heart NBT: " + e.getMessage());
        }

        return null;
    }
    
    /**
     * Class để lưu trữ config của Illusory Heart skill
     */
    private static class IllusoryHeartConfig {
        private final double damageBoost;
        private final double lightningDamage;
        private final double healPerSecond;
        private final double speedBoost;
        private final String clickType;
        private final int cooldown;

        public IllusoryHeartConfig(double damageBoost, double lightningDamage,
                                 double healPerSecond, double speedBoost, String clickType, int cooldown) {
            this.damageBoost = damageBoost;
            this.lightningDamage = lightningDamage;
            this.healPerSecond = healPerSecond;
            this.speedBoost = speedBoost;
            this.clickType = clickType;
            this.cooldown = cooldown;
        }

        public double getDamageBoost() { return damageBoost; }
        public double getLightningDamage() { return lightningDamage; }
        public double getHealPerSecond() { return healPerSecond; }
        public double getSpeedBoost() { return speedBoost; }
        public String getClickType() { return clickType; }
        public int getCooldown() { return cooldown; }
    }
}
