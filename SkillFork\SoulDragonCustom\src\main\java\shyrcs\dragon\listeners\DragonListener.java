package shyrcs.dragon.listeners;

import org.bukkit.Bukkit;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.entity.EnderDragon;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.entity.EntitySpawnEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.CustomDragon;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Listener for custom dragon events
 */
public class DragonListener implements Listener {
    
    private final SoulDragonPlugin plugin;
    private final Map<UUID, CustomDragon> customDragons;
    private final Map<UUID, BossBar> dragonBossBars;
    
    public DragonListener(SoulDragonPlugin plugin) {
        this.plugin = plugin;
        this.customDragons = new HashMap<>();
        this.dragonBossBars = new HashMap<>();
    }
    
    @EventHandler
    public void onEntitySpawn(EntitySpawnEvent event) {
        if (!(event.getEntity() instanceof EnderDragon dragon)) {
            return;
        }
        
        // Check if this is our custom dragon
        String dragonName = plugin.getConfig().getString("dragon.name", "§f§lRồng Ngàn Năm");
        if (dragon.customName() != null && 
            dragon.customName().toString().contains("Rồng Ngàn Năm")) {
            
            // Create custom dragon wrapper
            CustomDragon customDragon = new CustomDragon(plugin, dragon);
            customDragons.put(dragon.getUniqueId(), customDragon);
            
            // Create boss bar
            createBossBar(dragon);
            
            // Start custom AI
            customDragon.startAI();
            
            SoulDragonPlugin.info("Custom dragon spawned with AI: " + dragon.getUniqueId());
        }
    }
    
    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        if (!(event.getEntity() instanceof EnderDragon dragon)) {
            return;
        }
        
        UUID dragonId = dragon.getUniqueId();
        
        // Remove custom dragon
        CustomDragon customDragon = customDragons.remove(dragonId);
        if (customDragon != null) {
            customDragon.cleanup();
        }
        
        // Remove boss bar
        BossBar bossBar = dragonBossBars.remove(dragonId);
        if (bossBar != null) {
            bossBar.removeAll();
        }
        
        SoulDragonPlugin.info("Custom dragon died: " + dragonId);
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // Add player to all active boss bars
        for (BossBar bossBar : dragonBossBars.values()) {
            bossBar.addPlayer(player);
        }
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        // Remove player from all boss bars
        for (BossBar bossBar : dragonBossBars.values()) {
            bossBar.removePlayer(player);
        }
    }
    
    /**
     * Create boss bar for dragon
     */
    private void createBossBar(EnderDragon dragon) {
        String dragonName = plugin.getConfig().getString("dragon.name", "§f§lRồng Ngàn Năm");
        
        BossBar bossBar = Bukkit.createBossBar(
            dragonName,
            BarColor.WHITE,  // White color as requested
            BarStyle.SOLID
        );
        
        // Add all online players
        for (Player player : Bukkit.getOnlinePlayers()) {
            bossBar.addPlayer(player);
        }
        
        // Update boss bar health
        updateBossBarHealth(dragon, bossBar);
        
        dragonBossBars.put(dragon.getUniqueId(), bossBar);
        
        // Start health update task
        startHealthUpdateTask(dragon, bossBar);
    }
    
    /**
     * Update boss bar health
     */
    private void updateBossBarHealth(EnderDragon dragon, BossBar bossBar) {
        double healthPercentage = dragon.getHealth() / dragon.getMaxHealth();
        bossBar.setProgress(Math.max(0.0, Math.min(1.0, healthPercentage)));
    }
    
    /**
     * Start health update task for boss bar
     */
    private void startHealthUpdateTask(EnderDragon dragon, BossBar bossBar) {
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (dragon.isDead() || !dragon.isValid()) {
                return;
            }
            
            updateBossBarHealth(dragon, bossBar);
            
        }, 0L, 10L); // Update every 10 ticks (0.5 seconds)
    }
    
    /**
     * Get custom dragon by UUID
     */
    public CustomDragon getCustomDragon(UUID dragonId) {
        return customDragons.get(dragonId);
    }
    
    /**
     * Get all custom dragons
     */
    public Map<UUID, CustomDragon> getCustomDragons() {
        return new HashMap<>(customDragons);
    }
    
    /**
     * Cleanup all dragons and boss bars
     */
    public void cleanup() {
        // Cleanup all custom dragons
        for (CustomDragon customDragon : customDragons.values()) {
            customDragon.cleanup();
        }
        customDragons.clear();
        
        // Remove all boss bars
        for (BossBar bossBar : dragonBossBars.values()) {
            bossBar.removeAll();
        }
        dragonBossBars.clear();
    }
}
