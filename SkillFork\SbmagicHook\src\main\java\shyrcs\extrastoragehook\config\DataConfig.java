package shyrcs.extrastoragehook.config;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import shyrcs.extrastoragehook.SbMagicHook;

import java.io.File;
import java.util.List;

/**
 * Class quản lý cấu hình của plugin
 */
public class DataConfig {
    
    private final File configFile;
    private FileConfiguration config;
    
    public DataConfig(String path) {
        this.configFile = new File(path);
        loadConfig();
    }
    
    /**
     * Tải config từ file
     */
    private void loadConfig() {
        try {
            if (!configFile.exists()) {
                configFile.getParentFile().mkdirs();
                configFile.createNewFile();
            }
            this.config = YamlConfiguration.loadConfiguration(configFile);
        } catch (Exception e) {
            SbMagicHook.error("Không thể tải config: " + e.getMessage());
        }
    }
    
    /**
     * Lưu config
     */
    public void saveConfig() {
        try {
            config.save(configFile);
        } catch (Exception e) {
            SbMagicHook.error("Không thể lưu config: " + e.getMessage());
        }
    }
    
    /**
     * Reload config
     */
    public void reloadConfig() {
        this.config = YamlConfiguration.loadConfiguration(configFile);
    }
    
    /**
     * Lấy file config
     */
    public File getConfigFile() {
        return configFile;
    }
    
    // Discord Settings
    public String getBotToken() {
        return config.getString("discord.bot-token", "");
    }
    
    public String getGuildID() {
        return config.getString("discord.guild-id", "");
    }
    
    public String getActivity() {
        return config.getString("discord.activity.text", "với kho ExtraStorage");
    }
    
    public String getActivityType() {
        return config.getString("discord.activity.type", "PLAYING");
    }
    
    public boolean useSlashCommands() {
        return config.getBoolean("discord.use-slash-commands", true);
    }
    
    public List<String> getWhitelistedChannels() {
        return config.getStringList("discord.whitelisted-channels");
    }
    
    public int getCodeTimeout() {
        return config.getInt("discord.code-timeout", 300);
    }
    
    // Economy Settings
    public boolean usePriceProvider() {
        return config.getBoolean("economy.use-price-provider", true);
    }
    
    public String getEconomyProvider() {
        return config.getString("economy.provider", "ShopGUIPlus");
    }
    
    public String getMoneyFormat() {
        return config.getString("economy.money-format", "###,###.##");
    }
    
    // ExtraStorage Settings
    public boolean autoSellWhenFull() {
        return config.getBoolean("extrastorage.auto-sell-when-full", false);
    }
    
    public boolean allowSellAll() {
        return config.getBoolean("extrastorage.allow-sell-all", true);
    }
    
    public int getMaxSellAmount() {
        return config.getInt("extrastorage.max-sell-amount", 999999);
    }
    

    
    public String getColoredMessage(String key) {
        return SbMagicHook.color(getMessage(key));
    }
    
    // Commands
    public String getCommand(String key) {
        return config.getString("commands." + key, "Command not found: " + key);
    }
    
    // Emotes
    public String getEmote(String material) {
        return config.getString("emotes." + material.toUpperCase(), "📦");
    }
    
    // Utility methods
    public String getString(String path, String defaultValue) {
        return config.getString(path, defaultValue);
    }
    
    public int getInt(String path, int defaultValue) {
        return config.getInt(path, defaultValue);
    }
    
    public boolean getBoolean(String path, boolean defaultValue) {
        return config.getBoolean(path, defaultValue);
    }
    
    public List<String> getStringList(String path) {
        return config.getStringList(path);
    }
    
    public void set(String path, Object value) {
        config.set(path, value);
    }

    public String getMessage(String key) {
        return config.getString("messages." + key, "Message not found: " + key);
    }

    // ExtraStorage display settings
    public int getItemsPerPage() {
        return config.getInt("extrastorage.display.items-per-page", 10);
    }

    public List<String> getItemFilter() {
        return config.getStringList("extrastorage.display.item-filter");
    }

    public boolean isSellFilterOnly() {
        return config.getBoolean("extrastorage.display.sell-filter-only", true);
    }
}
