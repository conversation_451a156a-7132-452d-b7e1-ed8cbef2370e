package shyrcs.extrastoragehook.minecraft;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import shyrcs.extrastoragehook.application.Library;
import shyrcs.extrastoragehook.SbMagicHook;

/**
 * Command executor cho l<PERSON> ch<PERSON> c<PERSON>a plugin
 */
public class CommandSbMagicHook implements CommandExecutor {
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        
        if (args.length == 0) {
            sender.sendMessage(SbMagicHook.color("&6=== SbMagicHook ==="));
            sender.sendMessage(SbMagicHook.color("&e/sbmagichook hook <code> &7- <PERSON><PERSON>t n<PERSON>i tài khoản Discord"));
            sender.sendMessage(SbMagicHook.color("&e/sbmagichook reload &7- <PERSON><PERSON>i lại config"));
            sender.sendMessage(SbMagicHook.color("&e/sbmagichook info &7- Thông tin plugin"));
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "hook":
            case "connect":
                if (args.length < 2) {
                    sender.sendMessage(Library.config.getColoredMessage("invalid-code"));
                    return true;
                }
                hookCommand(sender, args[1]);
                break;
                
            case "reload":
                if (!sender.hasPermission("sbmagichook.reload")) {
                    sender.sendMessage(SbMagicHook.color("&cBạn không có quyền sử dụng lệnh này!"));
                    return true;
                }
                reloadCommand(sender);
                break;
                
            case "info":
                infoCommand(sender);
                break;
                
            default:
                sender.sendMessage(SbMagicHook.color("&cLệnh không hợp lệ! Sử dụng /sbmagichook để xem danh sách lệnh."));
                break;
        }
        
        return true;
    }
    
    /**
     * Lệnh kết nối tài khoản
     */
    public static void hookCommand(CommandSender sender, String code) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(Library.config.getColoredMessage("only-player"));
            return;
        }
        
        Player player = (Player) sender;
        
        if (!Library.bridge.keyExisted(code)) {
            sender.sendMessage(Library.config.getColoredMessage("invalid-code"));
            return;
        }
        
        String discordId = Library.bridge.getID(code.trim());

        // Lưu vào cả 2 nơi để đồng bộ
        Library.storage.connect(discordId, player.getUniqueId());
        Library.database.saveConnection(discordId, player.getUniqueId(), player.getName());

        Library.bridge.delete(code);

        player.sendMessage(Library.config.getColoredMessage("account-connected"));
        
        // Gửi thông báo đến Discord
        String note = Library.config.getMessage("discord-connected")
            .replace("{player}", player.getName());
        if (Library.application != null) {
            Library.application.sendPrivateMessage(discordId, note);
        }
        
        SbMagicHook.info("Người chơi " + player.getName() + " đã kết nối với Discord ID: " + discordId);
    }
    
    /**
     * Lệnh reload config
     */
    private void reloadCommand(CommandSender sender) {
        try {
            Library.config.reloadConfig();
            sender.sendMessage(SbMagicHook.color("&aĐã tải lại config thành công!"));
            SbMagicHook.info("Config đã được tải lại bởi " + sender.getName());
        } catch (Exception e) {
            sender.sendMessage(SbMagicHook.color("&cLỗi khi tải lại config: " + e.getMessage()));
            SbMagicHook.error("Lỗi khi tải lại config: " + e.getMessage());
        }
    }
    
    /**
     * Lệnh thông tin plugin
     */
    private void infoCommand(CommandSender sender) {
        sender.sendMessage(SbMagicHook.color("&6=== SbMagicHook Info ==="));
        sender.sendMessage(SbMagicHook.color("&eVersion: &f1.0.0-BETA"));
        sender.sendMessage(SbMagicHook.color("&eAuthor: &fShyrcs"));
        sender.sendMessage(SbMagicHook.color("&eDescription: &fKết nối ExtraStorage với Discord"));
        
        if (Library.storage != null) {
            sender.sendMessage(SbMagicHook.color("&eKết nối hiện tại: &f" + Library.storage.getConnectionCount()));
        }
        
        if (Library.extraStorageHook != null) {
            sender.sendMessage(SbMagicHook.color("&eExtraStorage: &aĐã kết nối"));
        } else {
            sender.sendMessage(SbMagicHook.color("&eExtraStorage: &cChưa kết nối"));
        }
        
        if (Library.application != null) {
            sender.sendMessage(SbMagicHook.color("&eDiscord Bot: &aĐang hoạt động"));
        } else {
            sender.sendMessage(SbMagicHook.color("&eDiscord Bot: &cKhông hoạt động"));
        }
    }
}
