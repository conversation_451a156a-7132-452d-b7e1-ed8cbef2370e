package shyrcs.Ability;

import net.Indyuce.mmoitems.api.item.build.ItemStackBuilder;
import net.Indyuce.mmoitems.api.item.mmoitem.ReadMMOItem;
import net.Indyuce.mmoitems.gui.edition.EditionInventory;
import net.Indyuce.mmoitems.stat.data.BooleanData;
import net.Indyuce.mmoitems.stat.type.BooleanStat;
import io.lumine.mythic.lib.api.item.ItemTag;
import io.lumine.mythic.lib.api.item.SupportedNBTTagValues;
import org.bukkit.Material;
import org.bukkit.event.inventory.InventoryClickEvent;
import java.util.*;

/**
 * Custom MMOItems stat để tạo chữ ký cho item
 * Chỉ chủ nhân item mới có thể sử dụng skills
 */
public class SignatureStat extends BooleanStat {
    
    public SignatureStat() {
        super("SIGNATURE",
              Material.NAME_TAG,
              "Signature",
              new String[]{
                  "Kích hoạt signature cho item.",
                  "<PERSON>ăn người khác sử dụng skills.",
                  "Staff có OP sẽ bỏ qua kiểm tra này.",
                  "",
                  "Click để toggle on/off."
              },
              new String[]{"weapon", "tool", "armor", "accessory", "all"}
        );
    }
    
    @Override
    public void whenApplied(ItemStackBuilder item, BooleanData data) {
        if (data.isEnabled()) {
            // Lưu giá trị vào NBT
            item.addItemTag(new ItemTag("MMOITEMS_SIGNATURE", true));

            // Thêm lore hiển thị signature status
            item.getLore().insert("signature", "§7Signature: §aEnabled");
        }
    }
    
    @Override
    public ArrayList<ItemTag> getAppliedNBT(BooleanData data) {
        ArrayList<ItemTag> tags = new ArrayList<>();
        if (data.isEnabled()) {
            tags.add(new ItemTag("MMOITEMS_SIGNATURE", true));
        }
        return tags;
    }
    

    
    @Override
    public BooleanData getLoadedNBT(ArrayList<ItemTag> tags) {
        ItemTag tag = ItemTag.getTagAtPath("MMOITEMS_SIGNATURE", tags);
        return tag != null ? new BooleanData((Boolean) tag.getValue()) : null;
    }
    
    @Override
    public void whenLoaded(ReadMMOItem mmoItem) {
        // Get tags - Following the pattern from other stats
        ArrayList<ItemTag> relevantTags = new ArrayList<>();

        if (mmoItem.getNBT().hasTag("MMOITEMS_SIGNATURE"))
            relevantTags.add(ItemTag.getTagAtPath("MMOITEMS_SIGNATURE", mmoItem.getNBT(), SupportedNBTTagValues.BOOLEAN));

        BooleanData data = getLoadedNBT(relevantTags);

        // Valid?
        if (data != null) {
            // Set
            mmoItem.setData(this, data);
        }
    }
    
    @Override
    public void whenClicked(EditionInventory inv, InventoryClickEvent event) {
        // Toggle boolean value
        boolean currentValue = inv.getEditedSection().getBoolean(getPath(), false);
        boolean newValue = !currentValue;

        inv.getEditedSection().set(getPath(), newValue);
        inv.registerTemplateEdition();
        inv.refreshInventory();

        String status = newValue ? "§aEnabled" : "§cDisabled";
        inv.getPlayer().sendMessage("§aSignature đã được đặt thành " + status);
    }
    
    @Override
    public BooleanData getClearStatData() {
        return new BooleanData(false);
    }
}
