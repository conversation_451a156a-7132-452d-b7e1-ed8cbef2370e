@echo off
echo Building SoulDragonCustom...

REM Check if <PERSON><PERSON> is available
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Maven is not installed or not in PATH
    echo Please install Maven and try again
    pause
    exit /b 1
)

REM Clean and compile
echo Cleaning previous build...
mvn clean

echo Compiling plugin...
mvn compile

echo Packaging plugin...
mvn package

if %errorlevel% equ 0 (
    echo.
    echo ================================
    echo Build successful!
    echo Plugin JAR location: target\SoulDragonCustom-1.0.0.jar
    echo ================================
) else (
    echo.
    echo ================================
    echo Build failed! Check errors above.
    echo ================================
)

pause
