###############################################
##                                           ##
##         WildStacker Configuration         ##
##            Developed by Ome_R             ##
##                                           ##
###############################################

# How should the item that is given to players by the give command be called?
# {0} represents stack size
# {1} represents entity/block type
# {2} represents item type (Egg / Spawner / Barrel)
give-item-name: '&6x{0} &f&o{1} {2}'

# The inspect tool of the plugin.
# When clicking an item, entity, barrel or spawner, all the information
# about the object will be displayed to the player.
inspect-tool:
  type: STICK
  name: '&6Inspect Tool'
  lore:
  - '&7Click on an object to get more details about it.'

# The simulate tool of the plugin.
# You can check if two objects can stack together using this tool.
simulate-tool:
  type: STICK
  name: '&6Simulate Tool'
  lore:
  - '&7Click on two objects to check if they can stack together.'

# Settings related to database.
database:
  # Should data of worlds that no longer exist be deleted?
  delete-invalid-worlds: true

# Settings related to the automatic kill all
kill-task:
  # How much time should be passed between auto-killing? (in seconds)
  # If you wish to disable the auto-killing task, set the interval to 0.
  interval: 300
  # Should the kill task remove stacked entities?
  stacked-entities: true
  # Should the kill task remove unstacked entities?
  unstacked-entities: true
  # Should the kill task remove stacked items?
  stacked-items: true
  # Should the kill task remove unstacked items?
  unstacked-items: true
  # When enabled, the plugin will remove all stacked-entities when clearlagg removes items & entities.
  # This feature will work if the interval is set to 0 - these are two different features!
  sync-clear-lagg: true
  # Set a command for getting the next time until kill task will happen.
  # You can split multiple commands using ",".
  # You can set it to '' in order to disable this feature.
  time-command: stacker timeleft
  # All settings related to the kill-all task of entities.
  kill-entities:
    # A list of entities that will be cleared.
    # EntityType list: https://bg-software.com/entities/
    # SpawnReason list: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html
    # You can combine both filters using "ENTITY_TYPE:SPAWN_REASON"
    # If you want all entities to be killed, set this to []
    whitelist: []
    # A list of entities that won't be cleared.
    # EntityType list: https://bg-software.com/entities/
    # SpawnReason list: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html
    # You can combine both filters using "ENTITY_TYPE:SPAWN_REASON"
    # If you wish to disable blacklisted entities, use "blacklist: []"
    blacklist:
    - AXOLOTL
    - CHICKEN
    - BEE
    - COW
    - PANDA
    - FOX
    - CAT
    - DONKEY
    - SHEEP
    # A list of worlds that entities will be cleared inside.
    # If you want all worlds, set this to []
    worlds:
    - SuperiorWorld
  # All settings related to the kill-all task of entities.
  kill-items:
    # A list of items that will be cleared.
    # Material list: https://bg-software.com/materials/
    # If you want all items to be killed, set this to []
    whitelist: []
    # A list of items that won't be cleared.
    # Material list: https://bg-software.com/materials/
    # If you wish to disable blacklisted items, use "blacklist: []"
    blacklist: []
    # A list of worlds that items will be cleared inside.
    # If you want all worlds, set this to []
    worlds: []

# Here you can configurable all features related to stacked items.
items:
  # Should items get stacked on the server?
  enabled: true

  # How many blocks from the item should be checked for other items to stack into?
  # Material list: https://bg-software.com/materials/
  # Make sure you follow the "TYPE" and "TYPE:DATA" formats.
  merge-radius:
    all: 5

  # Custom display-name for the items on ground.
  # If you don't want a display-name, use "custom-name: ''"
  # {0} represents stack amount
  # {1} represents display name
  # {2} represents display name in upper case
  custom-name: '&6x{0} &f&o{1}'

  # Blacklisted items are items that won't get stacked.
  # Material list: https://bg-software.com/materials/
  # Make sure you follow the "TYPE" and "TYPE:DATA" formats.
  # If you wish to disable blacklisted items, use "blacklist: []"
  blacklist:
  - DIAMOND_SWORD
  - IRON_SWORD
  - GOLDEN_SWORD
  - NETHERITE_SWORD
  - WOODEN_SWORD
  - STONE_SWORD
  - BOW
  - CROSSBOW
  - TRIDENT
  - SHIELD
  - DIAMOND_AXE
  - IRON_AXE
  - GOLDEN_AXE
  - NETHERITE_AXE
  - WOODEN_AXE
  - STONE_AXE
  - DIAMOND_PICKAXE
  - IRON_PICKAXE
  - GOLDEN_PICKAXE
  - NETHERITE_PICKAXE
  - WOODEN_PICKAXE
  - STONE_PICKAXE
  - DIAMOND_SHOVEL
  - IRON_SHOVEL
  - GOLDEN_SHOVEL
  - NETHERITE_SHOVEL
  - WOODEN_SHOVEL
  - STONE_SHOVEL
  - DIAMOND_HOE
  - IRON_HOE
  - GOLDEN_HOE
  - NETHERITE_HOE
  - WOODEN_HOE
  - STONE_HOE
  - TURTLE_HELMET
  - DIAMOND_HELMET
  - IRON_HELMET
  - GOLDEN_HELMET
  - NETHERITE_HELMET
  - CHAINMAIL_HELMET
  - LEATHER_HELMET
  - DIAMOND_CHESTPLATE
  - IRON_CHESTPLATE
  - GOLDEN_CHESTPLATE
  - NETHERITE_CHESTPLATE
  - CHAINMAIL_CHESTPLATE
  - LEATHER_CHESTPLATE
  - DIAMOND_LEGGINGS
  - IRON_LEGGINGS
  - GOLDEN_LEGGINGS
  - NETHERITE_LEGGINGS
  - CHAINMAIL_LEGGINGS
  - LEATHER_LEGGINGS
  - DIAMOND_BOOTS
  - IRON_BOOTS
  - GOLDEN_BOOTS
  - NETHERITE_BOOTS
  - CHAINMAIL_BOOTS
  - LEATHER_BOOTS
  - ELYTRA

  # Whitelisted items are items that will get stacked.
  # Material list: https://bg-software.com/materials/
  # Make sure you follow the "TYPE" and "TYPE:DATA" formats.
  # If you wish to disable whitelisted items, use "whitelist: []"
  whitelist: []

  # Set a maximum stack for specific items.
  # Make sure you follow the "TYPE" and "TYPE:DATA" formats.
  # You can use 'all' as a global limit (all: 20 will set all items to be limited to 20 per stack)
  # If you don't want any limits, you can set a random type.
  limits:
    all: 1024000

  # A list of worlds items won't get stacked inside them (case-sensitive)
  disabled-worlds:
  - dungeon
  - flatroom
  - minigame
  - pvp
  - world

  # Set a maximum amount of item objects in a chunk.
  # If you want to disable the feature, set it to 0.
  chunk-limit: 0

  # Should particles be spawned when an entity gets stacked?
  # If you want to edit the particles, check the particles file.
  particles: false

  # When enabled, all items will have a custom name (even if not stacked)
  unstacked-custom-name: true

  # When fix-stack is disabled, items with a max-stack of 1 will be added to inventories
  # with a max-stack size of 64. If a player picks up 80 picks, he will get 64 + 16, instead
  # of 80 different items.
  fix-stack: true

  # When item-display is enabled, the item's name will be displayed instead of it's type
  # This will take place on all items, and can only be overridden by custom-display section.
  item-display: true

  # Should players be able to disable item names for themselves?
  # In order to work, ProtocolLib should be installed.
  names-toggle:
    # Can item names be toggled?
    enabled: false

    # What the toggle command will be?
    command: stacker names item

  # Should pickup-sound be enabled for stacked items?
  pickup-sound: true

  # Should items with the max pickup delay get stacked (items that shouldn't be picked up in first place)
  max-pickup-delay: false

  # How much time should be passed between auto-stacking? (in ticks)
  # If you wish to disable the auto-stacking task, set the stack-interval to 0.
  # By default, all items are stacked once, when they spawn.
  stack-interval: 0

  # Should entities get stored into the database?
  store-items: true

# Here you can configurable all features related to stacked entities.
entities:
  # Should entities get stacked on the server?
  enabled: false

  # How many blocks from the entity should be checked for other entities to stack into?
  # EntityType list: https://bg-software.com/entities/
  # SpawnReason list: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html
  # You can combine both filters using "ENTITY_TYPE:SPAWN_REASON"
  merge-radius:
    all: 10

  # Custom display-name for the entities.
  # If you don't want a display-name, use "custom-name: ''"
  # {0} represents stack amount
  # {1} represents entity type
  # {2} represents entity type in upper case
  # {3} represents the upgrade's display name
  custom-name: '&6x{0} &f&o{1}{3}'

  # Blacklisted entities are entities that won't get stacked.
  # EntityType list: https://bg-software.com/entities/
  # SpawnReason list: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html
  # You can combine both filters using "ENTITY_TYPE:SPAWN_REASON"
  # If you wish to disable blacklisted entities, use "blacklist: []"
  blacklist:
  - VILLAGER

  # Whitelisted entities are entities that will get stacked.
  # EntityType list: https://bg-software.com/entities/
  # SpawnReason list: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html
  # You can combine both filters using "ENTITY_TYPE:SPAWN_REASON"
  # If you wish to disable whitelisted entities, use "whitelist: []"
  whitelist: []

  # Set a maximum stack for specific entities.
  # Make sure you follow the "ENTITY-TYPE" format.
  # You can use 'all' as a global limit (all: 20 will set all entities to be limited to 20 per stack)
  # If you don't want any limits, you can set a random type.
  limits:
    all: 1024000

  # Set a minimum required entities to stack for specific entities.
  # Make sure you follow the "ENTITY-TYPE" format.
  # You can use 'all' as a global limit (all: 20 will set all entities to must have at least 20 entities around before stacking)
  # If you don't want any limits, you can set a random type.
  minimum-required:
    EXAMPLE_ENTITY: 10

  # A list of worlds entities won't get stacked inside them (case-sensitive)
  disabled-worlds:
  - dungeon
  - flatroom
  - minigame
  - pvp
  - world
  - sundayboss
  - spawn

  # Set a maximum amount of entity objects in a chunk.
  # If you want to disable the feature, set it to 0.
  chunk-limit: 0

  # Should particles be spawned when an entity gets stacked?
  # If you want to edit the particles, check the particles file.
  particles: false

  # A list of WorldGuard regions entities won't get stacked inside them (case-sensitive)
  disabled-regions: []

  # Blacklisted names is a list of names that when an entity has this name, it won't get stacked.
  # Color codes are supported, as well as regex.
  # If you wish to enable stacking of all entities based on their name, use "name-blacklist: []"
  name-blacklist: []

  # How much time should be passed between auto-stacking? (in ticks)
  # If you wish to disable the auto-stacking task, set the stack-interval to 0.
  stack-interval: 0

  # A list of all checks that the plugin does before trying to stack two entities together.
  stack-checks:
    AGE: true
    ANIMAL_OWNER: true
    AXOLOTL_TYPE: true
    AXOLOTL_PLAYING_DEAD: false
    BAT_AWAKE: false
    CAN_BREED: true
    CAT_COLLAR_COLOR: true
    CAT_TYPE: true
    CHICKEN_TYPE: true
    COW_TYPE: true
    CREEPER_CHARGED: true
    ENDERMAN_CARRIED_BLOCK: true
    EXACT_AGE: false
    FROG_TOUNGE_TARGET: false
    FROG_TYPE: true
    GLOW_SQUID_DARK_TICKS: false
    GOAT_SCREAMING: false
    GUARDIAN_ELDER: true
    HORSE_CARRYING_CHEST: true
    HORSE_COLOR: true
    HORSE_JUMP: true
    HORSE_MAX_TAME_PROGRESS: true
    HORSE_STYLE: true
    HORSE_TAME_PROGRESS: true
    HORSE_TYPE: true
    IS_IN_LOVE: true
    IS_TAMED: true
    LLAMA_COLOR: true
    LLAMA_STRENGTH: true
    MOOSHROOM_TYPE: true
    NAME_TAG: true
    NERFED: true
    OCELOT_TYPE: true
    PARROT_TYPE: true
    PHANTOM_SIZE: true
    PIG_SADDLE: true
    PIG_TYPE: true
    PUFFERFISH_STATE: true
    RABBIT_TYPE: true
    SALMON_SIZE: true
    SHEEP_COLOR: true
    SHEEP_SHEARED: true
    SKELETON_TYPE: true
    SLIME_SIZE: true
    SPAWN_REASON: false
    TROPICALFISH_BODY_COLOR: true
    TROPICALFISH_TYPE: true
    TROPICALFISH_TYPE_COLOR: true
    UPGRADE: true
    VILLAGER_PROFESSION: true
    WOLF_ANGRY: false
    WOLF_COLLAR_COLOR: true
    WOLF_TYPE: true
    ZOMBIE_BABY: true
    ZOMBIE_PIGMAN_ANGRY: false

  # A list of all actions that the plugin check before unstacking an entity stack.
  stack-split:
    BEE_AGRO: false
    ENTITY_BREED: true
    ENTER_VEHICLE: true
    IRON_GOLEM_AGRO: false
    MUSHROOM_SHEAR: true
    NAME_TAG: true
    PIGMAN_AGRO: false
    SHEEP_DYE: true
    SHEEP_SHEAR: true
    VILLAGER_INFECTION: true
    WOLF_AGRO: false

  # Linked-entities are entities that are linked to one spawner or more.
  # A spawner that has an entity linked to, will try to stack it's entities first to the linked one.
  # Linked entities feature won't work if entities-stacking is disabled.
  linked-entities:
    # Should entities be linked to spawners?
    enabled: true

    # The maximum distance that the linked entity can be from the spawner.
    # If the entity is too far, it will get unlinked automatically from the spawner.
    max-distance: 10

  # Instant-kill will kill the entire stack instead of unstack it by one.
  # When an entire stack dies, their drops are getting multiplied.
  # EntityType list: https://bg-software.com/entities/
  # DamageCause list:  https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/event/entity/EntityDamageEvent.DamageCause.html
  # You can combine both filters using "ENTITY_TYPE:DAMAGE_CAUSE" or "ENTITY_TYPE:SPAWN_REASON"
  # If you don't want instant-kill, use "instant-kill: []"
  instant-kill:
  - FALL

  # Nerfed entities are entities that cannot attack / target other entities and players.
  # EntityType list: https://bg-software.com/entities/
  # SpawnReason list: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html
  # You can combine filters using "ENTITY_TYPE:SPAWN_REASON"
  nerfed-entities:
    # A list of entities that will be nerfed.
    # If you want all entities to be nerfed, set this to [].
    whitelist:
    - EXAMPLE_MOB
    # A list of entities that won't be nerfed.
    blacklist: []
    # A list of worlds that entities can be nerfed in.
    # If you want entities to be nerfed inside all worlds, set this to [].
    worlds:
    - SuperiorWorld
    # Should nerfed entities be able to teleport?
    teleport: false

  # Stack-down is a feature that will force entities to stack to other entities that are below their y level.
  # This feature is great for flying entities, like blazes and ghasts.
  stack-down:
    # Should the stack-down feature be enabled on the server?
    enabled: true
    # A list of entities that will be forced to only stack down.
    # You can combine filters using "ENTITY_TYPE:SPAWN_REASON"
    stack-down-types:
    - BLAZE
    - GHAST

  # If a stacked entity dies from fire, should the fire continue to the next entity?
  keep-fire: true

  # If enabled, the placeholder '{}' will be replaced with the stack amount for mythic mobs.
  mythic-mobs-custom-name: true

  # A list of entities that should keep the lowest health when stacking.
  # EntityType list: https://bg-software.com/entities/
  # SpawnReason list: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html
  # You can combine filters using "ENTITY_TYPE:SPAWN_REASON"
  keep-lowest-health: []

  # When enabled, parents of the entity will get stacked together after breeding.
  stack-after-breed: true

  # When smart-breed is enabled, players will be consumed with two items in order to breed a stack.
  # No breeding animation will be played, and a new baby stack will be spawned with the correct amount.
  # This can prevent massive splits of the stack for breeding, and can help with performance.
  smart-breeding:
    enabled: false
    # When enabled, items for breeding will be consumed from the entire inventory instead of only hand.
    consume-entire-inventory: false

  # When enabled, entities' names will be shown only if player looks exactly towards them.
  hide-names: false

  # Should players be able to disable entity names for themselves?
  # In order to work, ProtocolLib should be installed.
  names-toggle:
    # Can entity names be toggled?
    enabled: false

    # What the toggle command will be?
    command: stacker names entity

  # Should entities can be killed fast by players (No damage-cooldown)?
  fast-kill: true

  # The default amount entities should get unstacked by.
  # Use the "ENTITY_TYPE:AMOUNT" or "ENTITY_TYPE:SPAWN_CAUSE:AMOUNT"
  default-unstack:
    all: 1

  # Should exp get added directly to killer instead of dropped to ground?
  # EntityType list: https://bg-software.com/entities/
  # SpawnReason list: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html
  # You can combine both filters using "ENTITY_TYPE:SPAWN_REASON"
  auto-exp-pickup: []

  # Set a custom exp sound when auto-exp-pickup is enabled.
  # You can set this to '' if you want to disable it.
  exp-pickup-sound: ENTITY_EXPERIENCE_ORB_PICKUP

  # Should eggs that are laid by stacked chickens be multiplied by it's stack size?
  egg-lay-multiply: true

  # Should scout that are dropped by grown turtles by multiplied by it's stack size?
  scute-multiply: true

  # Should vanilla equipment of entities get cleared after they unstack?
  clear-equipment: false

  # Should corpses be spawned when entity dies ("death animations")?
  spawn-corpses: true

  # When enabled, entities will be one shot by specific tools.
  one-shot:
    # Should this feature be enabled?
    enabled: false
    # A list of tools that can one shot entities.
    tools:
    - DIAMOND_SWORD
    - DIAMOND_AXE
    # A list of entities that will be one shot.
    # EntityType list: https://bg-software.com/entities/
    # SpawnReason list: https://hub.spigotmc.org/javadocs/bukkit/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html
    # You can combine both filters using "ENTITY_TYPE:SPAWN_REASON"
    whitelist:
    - ALL

  # Should entities get stored into the database?
  store-entities: true

  # Should WildStacker add a custom entities-stacking setting to SuperiorSkyblock?
  # When enabling, make sure you add "entities_stacking" section to the settings menu.
  superiorskyblock-hook: false

  # Should drops get calculated in relation to the amount of mobs that died?
  multiply-drops: true

  # Should exp get calculated in relation to the amount of mobs that died?
  multiply-exp: true

  # Should damage be spread to the next stack?
  # If you deal more damage than the health of the entity, the rest of the damage will be dealt to the next stack.
  spread-damage: false

  # Which transform types should WildStacker listen to?
  # When an entity is transforming into another entity with one of the following reasons,
  # the plugin will convert it's data to the new entity.
  # A list of transform reasons can be find here:
  # https://hub.spigotmc.org/javadocs/spigot/org/bukkit/event/entity/EntityTransformEvent.TransformReason.html
  filtered-transforms:
  - DROWNED
  - CURED
  - PIGLIN_ZOMBIFIED
  - METAMORPHOSIS

  # Should entities fill vehicles entirely by stacked entities?
  # When enabled, vehicles will be able to get filled with two entities.
  # This can cause players to not be able to enter these vehicles.
  entities-fill-vehicles: false

# Here you can configurable all features related to stacked spawners.
spawners:
  # Should spawners get stacked on the server?
  enabled: false

  # How many blocks from the spawner should be checked for other spawners to stack into?
  # EntityType list: https://bg-software.com/entities/
  merge-radius:
    all: 1

  # Custom hologram for the spawners.
  # Holograms will be displayed only if one of the following plugins is enabled: HolographicDisplay, Holograms, Arconix
  # If you don't want a hologram, use "custom-name: ''"
  # {0} represents stack amount
  # {1} represents entity type
  # {2} represents entity type in upper case
  # {3} represents the upgrade's display name
  custom-name: '&6x{0} &f&o{1}{3}'

  # Blacklisted spawners are spawners that won't get stacked.
  # EntityType list: https://bg-software.com/entities/
  # If you wish to disable blacklisted spawners, use "blacklist: []"
  blacklist:
  - BLAZE

  # Whitelisted spawners are spawners that will get stacked.
  # EntityType list: https://bg-software.com/entities/
  # If you wish to disable whitelisted spawners, use "whitelist: []"
  whitelist: []

  # Set a maximum stack for specific spawners.
  # Make sure you follow the "ENTITY-TYPE" format.
  # You can use 'all' as a global limit (all: 20 will set all spawners to be limited to 20 per stack)
  # If you don't want any limits, you can set a random type.
  limits:
    EXAMPLE_ENTITY: 10

  # A list of worlds spawners won't get stacked inside them (case-sensitive)
  disabled-worlds:
  - dungeon
  - flatroom
  - minigame
  - pvp
  - world

  # Set a maximum amount of spawner objects in a chunk.
  # If you want to disable the feature, set it to 0.
  chunk-limit: 0

  # Should the chunk limit be per spawner or not?
  per-spawner-limit: false

  # Should particles be spawned when an entity gets stacked?
  # If you want to edit the particles, check the particles file.
  particles: false

  # When enabled, the plugin will try to find a spawner in the whole chunk instead
  # of only in the provided radius. merge-radius will be overridden, and will be used
  # as a y-level range only.
  chunk-merge: false

  # All settings related to the spawner items.
  spawner-item:
    # Custom name for the item.
    # If you don't want a custom name, use "custom-name: ''"
    # {0} represents stack amount
    # {1} represents entity type
    # {2} represents the upgrade's display name
    name: '&6x{0} &f&o{1} Spawners{2}'

    # Custom lore for the item.
    # If you don't want a custom lore, use "custom-lore: []"
    # {0} represents stack amount
    # {1} represents entity type
    lore: []

  # Here you can configurable all features related to silk-touch enchantment.
  # Other spawner plugins, such as SilkSpawners and MineableSpawners, will override this section.
  silk-touch:
    # Should spawners get dropped when mining them using a silk-touch pickaxe?
    enabled: true

    # Should the spawner item go straight into the player's inventory instead of dropping on ground?
    drop-to-inventory: true

    # A list of worlds that silk touch will work in.
    # If you want silk touch to work inside all the worlds, use "worlds: []"
    worlds: []

    # Should spawners get dropped without silktouch?
    # If enabled, only players with wildstacker.nosilkdrop will be able to get the spawners.
    drop-without-silk: false

    # Chance of spawners to be dropped after break of silk touch.
    break-chance: 100

    # The minimum required level for silk touch.
    minimum-level: 1

  # Here you can configurable all features related to exploded spawners.
  # Other spawner plugins, such as SilkSpawners, will override this section.
  explosions:
    # Should spawners get dropped when exploded by TNT or Creepers?
    enabled: true

    # Should the spawner item go straight into the player's inventory instead of dropping on ground?
    drop-to-inventory: true

    # A list of worlds that explosions will work in.
    # If you want explosions to work inside all the worlds, use "worlds: []"
    worlds: []

    # Chance of spawners to be dropped after exploded.
    break-chance: 100

  # Set the percentage amount that will be unstacked from explosions.
  # You can set this value to -1 and only one spawner will be broken everytime.
  explosions-break-percentage: 100

  # The minimum amount of spawners that will be unstacked.
  # It's used to fix small amounts of spawners not being unstacked by explosions when having
  # `explosions-break-percentage` below 100.
  explosions-break-minimum: 1

  # Set the percentage amount that will be dropped from explosions.
  explosions-amount-percentage: 100

  # The minimum amount of spawners that will be dropped.
  # It's used to fix small amounts of spawners not being dropped by explosions when having
  # `explosions-amount-percentage` below 100.
  explosions-amount-minimum: 1

  # Should silk touch must be required to mine spawners?
  mine-require-silk: false

  # Should sneaking while mining break the entire stack instead of reducing it by one?
  shift-get-whole-stack: true

  # Should one item be dropped for a stacked spawner instead of multiple items?
  # This feature will not work with other spawner-providers, such as SilkSpawners
  drop-stacked-item: true

  # When enabled, holograms will only be displayed when clicking on spawners.
  # They will be displayed for 3 seconds.
  # You must set a valid custom-name for it to work.
  floating-names: false

  # When enabled, you must have the permission wildstacker.place.<entity> to place an entity.
  # You can give a player the ability to place all spawners with wildstacker.place.*
  placement-permission: false

  # When enabled and player is placing a spawner while sneaking, all of the spawners the player
  # is holding will be placed instead of only 1.
  shift-place-stack: true

  # All settings related to break charge.
  # break-charge:
  #   EXAMPLE_MOB:                     The name of the mob.
  #     price: 0                       The price to charge upon breaking.
  #     multiply-stack-amount: false   Should the price get multiplied by the amount that were broken?
  break-charge:
    EXAMPLE_MOB:
      price: 0
      multiply-stack-amount: false

  # All settings related to break charge.
  # place-charge:
  #   EXAMPLE_MOB:                     The name of the mob.
  #     price: 0                       The price to charge upon placing.
  #     multiply-stack-amount: false   Should the price get multiplied by the amount that were placed?
  place-charge:
    EXAMPLE_MOB:
      price: 0
      multiply-stack-amount: false

  # When enabled, players will be able to change spawners by clicking them with spawn eggs.
  change-using-eggs: true

  # When enabled and change-using-eggs is enabled, the amount of eggs that will be required to change
  # a spawner would be the same as the stack size of the spawner.
  eggs-stack-multiply: true

  # Should there be the ability to place spawners next to each other?
  next-spawner-placement: true

  # Should there be only one spawner in the merge radius?
  only-one-spawner: true

  # All settings related to the inventory tweaks.
  # For example, right clicking a stacked item will split it into two stack in the inventory.
  inventory-tweaks:
    # Should there be inventory tweaks for spawner items?
    enabled: true
    # Set a permission to use the inventory tweaks.
    # You can set it to be empty in order to disable the feature.
    permission: ''
    # Set a command for a toggle mode for the inventory teaks.
    # You can split multiple commands using ",".
    # You can set it to be disabled in order to disable the feature.
    toggle-command: stacker inventorytweaks,stacker it

  # Settings related to the manage menu of spawners.
  # If all the sub menus are disabled, the manage menu will not be opened.
  manage-menu:
    # Whether sneaking is required in order to open the menu or not.
    sneaking-open-menu: true
    # Should the amounts menu be enabled?
    amounts-menu: true
    # Should the upgrades menu be enabled?
    upgrade-menu: true

  # Settings related to overriding spawners.
  spawners-override:
    # Should spawners behavior be overridden by WildStacker?
    # When enabled, you should see better performance from spawners.
    enabled: true
    # Configure spawn conditions for entities that are spawned by spawners
    # You can find more information regarding this by visiting the official wiki:
    # https://wiki.bg-software.com/#/wildstacker/spawn-conditions/
    spawn-conditions:
      EXAMPLE_MOB: []

  # All settings related to spawner upgrades.
  spawner-upgrades:
    # Should the cost to upgrade be multiplied by the stack amount of the spawner?
    multiply-stack-amount: true
    # Upgrade ladders
    ladders:
      '1':
        entities:
        - EXAMPLE_MOB
        default:
          next-upgrade: coal
          nearby-players: 16
          icon:
            type: STONE
            name: '&fBasic'
            lore:
            - '&fThis upgrade is free of charge'
            - '&7 '
            - '&7Spawn Delay Range&f: %min-spawn-delay%-%max-spawn-delay%'
            - '&7Spawn Count&f: %spawn-count%'
            - '&7Max Nearby Entities&f: %max-nearby-entities%'
            - '&7Required Player Range&f: %required-player-range%'
            - '&7Spawn Range&f: %spawn-range%'
        coal:
          id: 1
          next-upgrade: iron
          display: '&7 (&8Coal&7)'
          cost: 10000
          icon:
            type: COAL
            name: '&8Coal'
            lore:
            - '&8This upgrade costs $%cost%'
            - '&7 '
            - '&7Spawn Delay Range&f: &8%min-spawn-delay%-%max-spawn-delay%'
            - '&7Spawn Count&f: &8%spawn-count%'
            - '&7Max Nearby Entities&f: &8%max-nearby-entities%'
            - '&7Required Player Range&f: &8%required-player-range%'
            - '&7Spawn Range&f: &8%spawn-range%'
        iron:
          id: 2
          next-upgrade: gold
          display: '&7 (Iron)'
          cost: 25000
          icon:
            type: IRON_INGOT
            name: '&7Iron'
            lore:
            - '&7This upgrade costs $%cost%'
            - '&7 '
            - '&7Spawn Delay Range&f: &7%min-spawn-delay%-%max-spawn-delay%'
            - '&7Spawn Count&f: &7%spawn-count%'
            - '&7Max Nearby Entities&f: &7%max-nearby-entities%'
            - '&7Required Player Range&f: &7%required-player-range%'
            - '&7Spawn Range&f: &7%spawn-range%'
        gold:
          id: 3
          next-upgrade: diamond
          display: '&7 (&6Gold&7)'
          cost: 75000
          icon:
            type: GOLD_INGOT
            name: '&6Gold'
            lore:
            - '&6This upgrade costs $%cost%'
            - '&7 '
            - '&7Spawn Delay Range&f: &6%min-spawn-delay%-%max-spawn-delay%'
            - '&7Spawn Count&f: &6%spawn-count%'
            - '&7Max Nearby Entities&f: &6%max-nearby-entities%'
            - '&7Required Player Range&f: &6%required-player-range%'
            - '&7Spawn Range&f: &6%spawn-range%'
        diamond:
          id: 4
          display: '&7 (&bDiamond&7)'
          cost: 120000
          icon:
            type: DIAMOND
            name: '&bDiamond'
            lore:
            - '&bThis upgrade costs $%cost%'
            - '&7 '
            - '&7Spawn Delay Range&f: &b%min-spawn-delay%-%max-spawn-delay%'
            - '&7Spawn Count&f: &b%spawn-count%'
            - '&7Max Nearby Entities&f: &b%max-nearby-entities%'
            - '&7Required Player Range&f: &b%required-player-range%'
            - '&7Spawn Range&f: &b%spawn-range%'

  # Should the plugin listen to Paper's PreSpawnerSpawnEvent and stack entities there?
  # If this feature is enabled, entity attributes will not be checked, but only types will be considered for stacking.
  listen-paper-pre-spawn-event: true

  # When enabled, all spawners will have a custom name (even if not stacked)
  unstacked-custom-name: false
  break-menu: {}

# Here you can configurable all features related to stacked barrels (aka stacked blocks).
barrels:
  # Should blocks get stacked into barrels on the server?
  enabled: false

  # How many blocks from the barrel should be checked for other blocks to stack into?
  # Material list: https://bg-software.com/materials/
  # Make sure you follow the "TYPE" and "TYPE:DATA" formats.
  merge-radius:
    all: 1

  # Custom hologram for the barrels.
  # Holograms will be displayed only if one of the following plugins is enabled: HolographicDisplay, Holograms, Arconix
  # If you don't want a hologram, use "custom-name: ''"
  # {0} represents stack amount
  # {1} represents barrel type
  # {2} represents barrel type in upper case
  custom-name: '&6x{0} &f&o{1}'

  # Blacklisted barrels are barrels that won't get stacked.
  # Material list: https://bg-software.com/materials/
  # Make sure you follow the "TYPE" and "TYPE:DATA" formats.
  # If you wish to disable blacklisted barrels, use "blacklist: []"
  blacklist: []

  # Whitelisted blocks are blocks that will get stacked.
  # Material list: https://bg-software.com/materials/
  # Make sure you follow the "TYPE" and "TYPE:DATA" formats.
  # If you wish to disable whitelisted barrels, use "whitelist: []"
  whitelist:
  - EXAMPLE_BARREL

  # Set a maximum stack for a specific barrel.
  # Make sure you follow the "TYPE" and "TYPE:DATA" formats.
  # You can use 'all' as a global limit (all: 20 will set all barrels to be limited to 20 per stack)
  # If you don't want any limits, you can set a random type.
  limits:
    EXAMPLE_BARREL: 100

  # A list of worlds barrels won't get stacked inside them (case-sensitive)
  disabled-worlds:
  - disabled_world

  # Set a maximum amount of barrel objects in a chunk.
  # If you want to disable the feature, set it to 0.
  chunk-limit: 0

  # Should particles be spawned when an entity gets stacked?
  # If you want to edit the particles, check the particles file.
  particles: false

  # When enabled, the plugin will try to find a block in the whole chunk instead
  # of only in the provided radius. merge-radius will be overridden, and will be used
  # as a y-level range only.
  chunk-merge: false

  # Should explosions break the entire stack, or just reducing it by one?
  explosions-break-stack: true

  # Here you can configurable all features related to toggle commands.
  toggle-command:
    # Should toggle-commands be enabled?
    enabled: false

    # What the toggle command will be?
    command: stacker toggle

  # When enabled and player is clicking a barrel while sneaking, an inventory will be opened, there
  # he can put blocks to add to the barrel.
  place-inventory:
    enabled: true
    title: Add items here ({0})

  # This should not be set to true unless being told by the dev.
  # When enabled, the plugin sets the barrel types to cauldron if they aren't already.
  force-cauldron: false

  # Set a required permission for barrels.
  # If you want to disable it, use "required-permission: ''"
  required-permission: ''

  # Should items from barrels get added directed into the player's inventory?
  auto-pickup: false

  # Should a stacked item be dropped when a barrel is broken?
  drop-stacked-item: false

  # When enabled and player is placing a barrel while sneaking, all of the barrel the player
  # is holding will be placed instead of only 1.
  shift-place-stack: false

# Here you can configurable all features related to stacked buckets
buckets:
  # Should buckets get stacked on the server?
  enabled: true

  # A list of blacklisted bucket names.
  name-blacklist:
  - '&fGenbucket'

  # The new max-stack size for buckets. Must be a number between 1 and 64.
  max-stack: 16

# Here you can configurable all features related to stacked stews
stews:
  # Should stews get stacked on the server?
  enabled: true

  # The new max-stack size for stews. Must be a number between 1 and 64.
  max-stack: 16