package shyrcs.Ability;

import io.lumine.mythic.lib.api.item.NBTItem;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import java.util.*;

/**
 * Utility class để xử lý placeholder cho Signature stat
 */
public class SignaturePlaceholderUtil {
    
    /**
     * Kiểm tra xem item có placeholder signature không
     */
    public static boolean hasSignaturePlaceholder(ItemStack item) {
        if (item == null || !item.hasItemMeta()) return false;
        
        ItemMeta meta = item.getItemMeta();
        if (!meta.hasLore()) return false;
        
        // Sử dụng deprecated method vì chưa có alternative tốt hơn
        @SuppressWarnings("deprecation")
        List<String> lore = meta.getLore();
        if (lore == null) return false;
        
        // Kiểm tra xem có placeholder {signature_player} không
        for (String line : lore) {
            if (line.contains("{signature_player}")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Xử lý placeholder trong lore của item
     * Thay thế {signature_player} bằng tên chủ nhân
     */
    public static ItemStack processSignaturePlaceholder(ItemStack item, String ownerName) {
        if (item == null || !item.hasItemMeta()) return item;
        
        ItemMeta meta = item.getItemMeta();
        if (!meta.hasLore()) return item;
        
        List<String> lore = new ArrayList<>();
        if (meta.hasLore()) {
            // Sử dụng deprecated method vì chưa có alternative tốt hơn
            @SuppressWarnings("deprecation")
            List<String> legacyLore = meta.getLore();
            if (legacyLore != null) {
                lore.addAll(legacyLore);
            }
        }
        if (lore.isEmpty()) return item;
        
        // Xử lý từng dòng lore
        List<String> newLore = new ArrayList<>();
        boolean hasChanges = false;
        
        for (String line : lore) {
            String processedLine = line;
            
            // Thay thế placeholder
            if (processedLine.contains("{signature_player}")) {
                processedLine = processedLine.replace("{signature_player}", ownerName != null ? ownerName : "Unknown");
                hasChanges = true;
            }
            
            newLore.add(processedLine);
        }
        
        // Cập nhật lore nếu có thay đổi
        if (hasChanges) {
            // Sử dụng deprecated method vì chưa có alternative tốt hơn
            List<String> finalLore = newLore;
            meta.setLore(finalLore);
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Kiểm tra xem item có signature stat không
     */
    public static boolean hasSignatureStat(ItemStack item) {
        if (item == null) return false;
        
        try {
            NBTItem nbtItem = NBTItem.get(item);
            return nbtItem.hasTag("MMOITEMS_SIGNATURE") && nbtItem.getBoolean("MMOITEMS_SIGNATURE");
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Lấy tên chủ nhân từ NBT của item (không còn sử dụng SignatureOwnerStat)
     */
    public static String getOwnerName(ItemStack item) {
        // Signature system đã được đơn giản hóa
        // Không còn cần logic lấy owner name riêng
        return null;
    }
    
    /**
     * Đặt tên chủ nhân vào NBT của item (simplified approach)
     * Note: This is a placeholder - actual signature setting should be done via MMOItems commands
     */
    public static ItemStack setOwnerName(ItemStack item, String ownerName) {
        if (item == null || ownerName == null) return item;

        // For now, we'll just process the placeholder in the lore
        // The actual owner setting should be done via MMOItems commands when creating the item
        return processSignaturePlaceholder(item, ownerName);
    }
    
    /**
     * Kiểm tra xem player có phải chủ nhân item không (đã đơn giản hóa)
     */
    public static boolean isOwner(ItemStack item, String playerName) {
        // Signature system đã được đơn giản hóa
        // Không còn logic kiểm tra owner
        return false;
    }
    
    /**
     * Kiểm tra xem item có cần được ký không (có signature stat nhưng chưa có owner)
     */
    public static boolean needsSignature(ItemStack item) {
        return hasSignatureStat(item) && getOwnerName(item) == null;
    }
}
