package shyrcs.extrastoragehook.database;

import shyrcs.extrastoragehook.SbMagicHook;
import java.io.File;
import java.sql.*;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * LiteSQL database để lưu connections giữa Discord và Minecraft
 */
public class LiteDatabase {
    
    private final File databaseFile;
    private Connection connection;
    
    public LiteDatabase(File dataFolder) {
        this.databaseFile = new File(dataFolder, "connections.db");
        initializeDatabase();
    }
    
    /**
     * Khởi tạo database và tạo bảng
     */
    private void initializeDatabase() {
        try {
            // Tạo thư mục nếu chưa tồn tại
            if (!databaseFile.getParentFile().exists()) {
                databaseFile.getParentFile().mkdirs();
            }
            
            // Kết nối SQLite
            String url = "jdbc:sqlite:" + databaseFile.getAbsolutePath();
            connection = DriverManager.getConnection(url);
            
            // Tạo bảng connections
            String createTable = """
                CREATE TABLE IF NOT EXISTS connections (
                    discord_id TEXT PRIMARY KEY,
                    minecraft_uuid TEXT NOT NULL,
                    minecraft_name TEXT NOT NULL,
                    connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """;
            
            try (Statement stmt = connection.createStatement()) {
                stmt.execute(createTable);
            }
            
            SbMagicHook.info("Database initialized successfully");
            
        } catch (SQLException e) {
            SbMagicHook.error("Failed to initialize database: " + e.getMessage());
        }
    }
    
    /**
     * Lưu connection
     */
    public boolean saveConnection(String discordId, UUID minecraftUuid, String minecraftName) {
        String sql = "INSERT OR REPLACE INTO connections (discord_id, minecraft_uuid, minecraft_name) VALUES (?, ?, ?)";
        
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, discordId);
            pstmt.setString(2, minecraftUuid.toString());
            pstmt.setString(3, minecraftName);
            
            int affected = pstmt.executeUpdate();
            return affected > 0;
            
        } catch (SQLException e) {
            SbMagicHook.error("Failed to save connection: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Xóa connection
     */
    public boolean removeConnection(String discordId) {
        String sql = "DELETE FROM connections WHERE discord_id = ?";
        
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, discordId);
            
            int affected = pstmt.executeUpdate();
            return affected > 0;
            
        } catch (SQLException e) {
            SbMagicHook.error("Failed to remove connection: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Kiểm tra Discord user đã kết nối chưa
     */
    public boolean isConnected(String discordId) {
        String sql = "SELECT 1 FROM connections WHERE discord_id = ?";
        
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, discordId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                return rs.next();
            }
            
        } catch (SQLException e) {
            SbMagicHook.error("Failed to check connection: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Lấy Minecraft UUID từ Discord ID
     */
    public UUID getMinecraftUUID(String discordId) {
        String sql = "SELECT minecraft_uuid FROM connections WHERE discord_id = ?";
        
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, discordId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return UUID.fromString(rs.getString("minecraft_uuid"));
                }
            }
            
        } catch (SQLException e) {
            SbMagicHook.error("Failed to get Minecraft UUID: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Lấy Minecraft name từ Discord ID
     */
    public String getMinecraftName(String discordId) {
        String sql = "SELECT minecraft_name FROM connections WHERE discord_id = ?";
        
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, discordId);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("minecraft_name");
                }
            }
            
        } catch (SQLException e) {
            SbMagicHook.error("Failed to get Minecraft name: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Lấy Discord ID từ Minecraft UUID
     */
    public String getDiscordID(UUID minecraftUuid) {
        String sql = "SELECT discord_id FROM connections WHERE minecraft_uuid = ?";
        
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, minecraftUuid.toString());
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("discord_id");
                }
            }
            
        } catch (SQLException e) {
            SbMagicHook.error("Failed to get Discord ID: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Lấy tất cả connections
     */
    public Map<String, UUID> getAllConnections() {
        Map<String, UUID> connections = new HashMap<>();
        String sql = "SELECT discord_id, minecraft_uuid FROM connections";
        
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                String discordId = rs.getString("discord_id");
                UUID minecraftUuid = UUID.fromString(rs.getString("minecraft_uuid"));
                connections.put(discordId, minecraftUuid);
            }
            
        } catch (SQLException e) {
            SbMagicHook.error("Failed to get all connections: " + e.getMessage());
        }
        
        return connections;
    }
    
    /**
     * Lấy số lượng connections
     */
    public int getConnectionCount() {
        String sql = "SELECT COUNT(*) FROM connections";
        
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
            
        } catch (SQLException e) {
            SbMagicHook.error("Failed to get connection count: " + e.getMessage());
        }
        
        return 0;
    }
    
    /**
     * Đóng database connection
     */
    public void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            SbMagicHook.error("Failed to close database: " + e.getMessage());
        }
    }
}
